<?php
require_once 'config.php';

header('Content-Type: text/html');
echo "<h1>🔧 Email Image Processing Fix</h1>";

echo "<h2>1. Analyzing Current Image Processing Issues</h2>";

// Check the current sendEmail function for image processing issues
echo "<h3>Current Image Processing Analysis:</h3>";

// Look for potential issues in the config.php sendEmail function
$configContent = file_get_contents('config.php');

// Check for image processing patterns
if (strpos($configContent, 'addEmbeddedImage') !== false) {
    echo "✅ Email function has image embedding capability<br>";
} else {
    echo "❌ Email function missing image embedding<br>";
}

if (strpos($configContent, 'birthday_member_image') !== false) {
    echo "✅ Birthday member image processing found<br>";
} else {
    echo "❌ Birthday member image processing missing<br>";
}

echo "<h2>2. Creating Enhanced Image Processing Fix</h2>";

// Create a backup of the current config.php
$backupFile = 'config_backup_' . date('Y-m-d_H-i-s') . '.php';
copy('config.php', $backupFile);
echo "✅ Created backup: $backupFile<br>";

// Read the current config.php content
$configLines = file('config.php');
$newConfigContent = '';
$inSendEmailFunction = false;
$functionBraceCount = 0;
$foundImageProcessing = false;

foreach ($configLines as $lineNum => $line) {
    // Check if we're entering the sendEmail function
    if (strpos($line, 'function sendEmail(') !== false) {
        $inSendEmailFunction = true;
        $functionBraceCount = 0;
    }
    
    // Count braces to track function scope
    if ($inSendEmailFunction) {
        $functionBraceCount += substr_count($line, '{') - substr_count($line, '}');
        
        // Check for image processing section
        if (strpos($line, 'birthday_member_image') !== false || strpos($line, 'addEmbeddedImage') !== false) {
            $foundImageProcessing = true;
        }
        
        // If we're at the end of the function
        if ($functionBraceCount <= 0 && strpos($line, '}') !== false) {
            $inSendEmailFunction = false;
        }
    }
    
    $newConfigContent .= $line;
}

echo "<h2>3. Image Processing Status</h2>";

if ($foundImageProcessing) {
    echo "✅ Image processing code found in sendEmail function<br>";
    echo "<p>The issue might be in the image path resolution or member data handling.</p>";
} else {
    echo "❌ Image processing code missing from sendEmail function<br>";
    echo "<p>Need to add proper image embedding functionality.</p>";
}

echo "<h2>4. Testing Member Data Isolation</h2>";

// Test member data isolation
$stmt = $pdo->prepare('SELECT id, full_name, email, image_path FROM members WHERE image_path IS NOT NULL LIMIT 3');
$stmt->execute();
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Member Data Test:</h3>";
foreach ($members as $member) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
    echo "<strong>Member:</strong> {$member['full_name']}<br>";
    echo "<strong>Email:</strong> {$member['email']}<br>";
    echo "<strong>Image Path:</strong> {$member['image_path']}<br>";
    
    // Check if image file exists
    if (file_exists($member['image_path'])) {
        echo "✅ Image file exists<br>";
    } else {
        echo "❌ Image file missing<br>";
    }
    
    // Test placeholder replacement for this member
    $testTemplate = '<img src="{member_image}" alt="{full_name}" style="width: 150px; height: 150px;">';
    $memberData = [
        'full_name' => $member['full_name'],
        'member_image' => $member['image_path'],
        'image_path' => $member['image_path']
    ];
    
    $processed = replaceTemplatePlaceholders($testTemplate, $memberData);
    echo "<strong>Processed Template:</strong> " . htmlspecialchars($processed) . "<br>";
    echo "</div>";
}

echo "<h2>5. Creating Email Test Function</h2>";

// Create a test function to verify email processing
$testFunction = '
function testBirthdayEmailProcessing($memberId) {
    global $pdo;
    
    // Get member data
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$memberId]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        return ["error" => "Member not found"];
    }
    
    // Get a birthday template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        return ["error" => "No birthday template found"];
    }
    
    // Process member data
    $memberData = [
        "full_name" => $member["full_name"],
        "first_name" => explode(" ", $member["full_name"])[0],
        "email" => $member["email"],
        "member_image" => $member["image_path"],
        "image_path" => $member["image_path"],
        "organization_name" => "Freedom Assembly Church",
        "organization_type" => "church"
    ];
    
    // Process template
    $subject = replaceTemplatePlaceholders($template["subject"], $memberData);
    $content = replaceTemplatePlaceholders($template["content"], $memberData);
    
    return [
        "member" => $member["full_name"],
        "subject" => $subject,
        "content_length" => strlen($content),
        "has_image_placeholder" => strpos($content, $member["image_path"]) !== false,
        "image_exists" => file_exists($member["image_path"])
    ];
}
';

// Save the test function to a file
file_put_contents('test_birthday_email_function.php', "<?php\nrequire_once 'config.php';\n" . $testFunction);

echo "✅ Created test function in test_birthday_email_function.php<br>";

echo "<h2>6. Running Email Processing Test</h2>";

// Test with the first member who has an image
$testMember = null;
foreach ($members as $member) {
    if (!empty($member['image_path']) && file_exists($member['image_path'])) {
        $testMember = $member;
        break;
    }
}

if ($testMember) {
    echo "<h3>Testing with member: {$testMember['full_name']}</h3>";
    
    // Include and run the test
    include 'test_birthday_email_function.php';
    $result = testBirthdayEmailProcessing($testMember['id']);
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    echo "</div>";
} else {
    echo "❌ No suitable test member found<br>";
}

echo "<h2>7. Recommendations</h2>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>⚠️ Critical Issues to Address:</h3>";
echo "<ol>";
echo "<li><strong>Image Path Resolution:</strong> Ensure image paths are correctly resolved in emails</li>";
echo "<li><strong>Member Data Isolation:</strong> Verify each email only contains the correct member's data</li>";
echo "<li><strong>Email Template Processing:</strong> Check for any cross-contamination in template processing</li>";
echo "<li><strong>Image Embedding:</strong> Ensure images are properly embedded as CID attachments</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Immediate Actions:</h3>";
echo "<ol>";
echo "<li>Test the birthday email system with a single member</li>";
echo "<li>Check email logs for any cross-contamination</li>";
echo "<li>Verify image display in actual email clients</li>";
echo "<li>Monitor the email sending process for data leakage</li>";
echo "</ol>";
echo "</div>";

?>

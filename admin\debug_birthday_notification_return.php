<?php
/**
 * Debug script to examine the return value from sendMemberBirthdayNotifications
 * and identify why error messages are still appearing when emails are sent successfully
 */

session_start();
require_once '../config.php';
require_once '../send_birthday_reminders.php';

echo "<h2>Debug Birthday Notification Return Values</h2>\n";

// Test with a real birthday member
try {
    // Get a member with a birthday
    $stmt = $pdo->prepare("SELECT * FROM members WHERE birth_date IS NOT NULL AND email IS NOT NULL LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testMember) {
        echo "<p>❌ No test member found with birth_date and email</p>\n";
        exit;
    }
    
    echo "<h3>Test Member Information</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>ID:</strong> {$testMember['id']}</li>\n";
    echo "<li><strong>Name:</strong> {$testMember['full_name']}</li>\n";
    echo "<li><strong>Email:</strong> {$testMember['email']}</li>\n";
    echo "<li><strong>Birth Date:</strong> {$testMember['birth_date']}</li>\n";
    echo "</ul>\n";
    
    // Get a notification template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Notification%' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ No notification template found</p>\n";
        exit;
    }
    
    echo "<h3>Template Information</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>ID:</strong> {$template['id']}</li>\n";
    echo "<li><strong>Name:</strong> {$template['template_name']}</li>\n";
    echo "</ul>\n";
    
    // Create BirthdayReminder instance
    $birthdayReminder = new BirthdayReminder($pdo);
    
    echo "<h3>Testing sendMemberBirthdayNotifications Method</h3>\n";
    
    // Test the method call
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $testMember['id'],
        $template['id'],
        1 // 1 day until birthday
    );
    
    echo "<h4>Raw Return Value:</h4>\n";
    echo "<pre style='background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>\n";
    echo htmlspecialchars(print_r($result, true));
    echo "</pre>\n";
    
    echo "<h4>Return Value Analysis:</h4>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Key</th><th>Value</th><th>Type</th></tr>\n";
    
    if (is_array($result)) {
        foreach ($result as $key => $value) {
            $type = gettype($value);
            $displayValue = is_array($value) ? 'Array(' . count($value) . ')' : htmlspecialchars($value);
            echo "<tr><td><strong>$key</strong></td><td>$displayValue</td><td>$type</td></tr>\n";
        }
    } else {
        echo "<tr><td colspan='3'>Result is not an array: " . gettype($result) . "</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h4>Message Logic Test:</h4>\n";
    
    // Apply the same logic as in admin/send_birthday_notification.php
    $success_message = '';
    $error_message = '';
    
    if (isset($result['error'])) {
        $error_message = $result['error'];
        echo "<p>🔴 <strong>Error path taken:</strong> {$result['error']}</p>\n";
    } elseif (isset($result['success']) && $result['success'] > 0) {
        // Success: At least one email was sent successfully
        $success_message = "Successfully sent {$result['success']} notification emails about {$result['birthday_member']}'s birthday!";
        if (isset($result['failed']) && $result['failed'] > 0) {
            $success_message .= " ({$result['failed']} failed)";
        }
        echo "<p>🟢 <strong>Success path taken:</strong> $success_message</p>\n";
    } elseif (isset($result['message'])) {
        // No emails sent but with a specific message
        $error_message = $result['message'];
        echo "<p>🟡 <strong>Message path taken:</strong> {$result['message']}</p>\n";
    } else {
        // No emails sent and no specific message
        $error_message = "No notification emails were sent. Please check if there are eligible recipients.";
        echo "<p>🔴 <strong>Default error path taken:</strong> $error_message</p>\n";
    }
    
    echo "<h4>Final Message Display:</h4>\n";
    if (!empty($success_message)) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px;'>\n";
        echo "<strong>SUCCESS:</strong> $success_message\n";
        echo "</div>\n";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>\n";
        echo "<strong>ERROR:</strong> $error_message\n";
        echo "</div>\n";
    }
    
    echo "<h4>AJAX Response Simulation:</h4>\n";
    $ajaxResponse = [
        'success' => !empty($success_message),
        'message' => !empty($success_message) ? $success_message : $error_message
    ];
    
    echo "<pre style='background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>\n";
    echo htmlspecialchars(json_encode($ajaxResponse, JSON_PRETTY_PRINT));
    echo "</pre>\n";
    
    // Check for potential issues
    echo "<h3>Potential Issues Analysis</h3>\n";
    echo "<ul>\n";
    
    if (!is_array($result)) {
        echo "<li>❌ <strong>Critical:</strong> Method returned " . gettype($result) . " instead of array</li>\n";
    }
    
    if (isset($result['success']) && $result['success'] === 0) {
        echo "<li>⚠️ <strong>Warning:</strong> Success count is 0 - no emails were actually sent</li>\n";
    }
    
    if (isset($result['error']) && isset($result['success']) && $result['success'] > 0) {
        echo "<li>⚠️ <strong>Inconsistency:</strong> Both error and success > 0 present</li>\n";
    }
    
    if (!isset($result['success']) && !isset($result['error'])) {
        echo "<li>❌ <strong>Missing Keys:</strong> Neither 'success' nor 'error' key found</li>\n";
    }
    
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Exception occurred:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<p><a href='send_birthday_notification.php'>← Back to Birthday Notifications Page</a></p>\n";
?>

<?php
require_once 'config.php';

header('Content-Type: text/plain');
echo "=== BIRTHDAY EMAIL SYSTEM AUDIT ===\n\n";

try {
    // 1. Check birthday templates
    echo "1. BIRTHDAY EMAIL TEMPLATES:\n";
    echo "----------------------------\n";
    $stmt = $pdo->prepare('SELECT id, template_name, subject, content, is_birthday_template FROM email_templates WHERE is_birthday_template = 1 ORDER BY id');
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "❌ No birthday templates found!\n\n";
    } else {
        foreach ($templates as $template) {
            echo "Template ID: {$template['id']}\n";
            echo "Name: {$template['template_name']}\n";
            echo "Subject: {$template['subject']}\n";
            echo "Content Length: " . strlen($template['content']) . " characters\n";
            
            // Check for common issues
            $issues = [];
            if (strpos($template['content'], 'alt="Jennifer Godson"') !== false) {
                $issues[] = "❌ Contains hardcoded member name in alt text";
            }
            if (strpos($template['content'], 'style="width: 100px; height: 100px;') !== false) {
                $issues[] = "❌ Contains broken image styling";
            }
            if (strpos($template['content'], '{member_image}') === false && strpos($template['content'], '{birthday_member_image}') === false) {
                $issues[] = "⚠️ No member image placeholder found";
            }
            if (strpos($template['content'], 'Member"') !== false) {
                $issues[] = "❌ Contains broken image tag";
            }
            
            if (!empty($issues)) {
                echo "Issues found:\n";
                foreach ($issues as $issue) {
                    echo "  $issue\n";
                }
            } else {
                echo "✅ No obvious issues found\n";
            }
            
            // Show first 200 characters of content for inspection
            echo "Content preview:\n";
            echo substr($template['content'], 0, 200) . "...\n";
            echo "\n";
        }
    }
    
    // 2. Check recent birthday emails sent
    echo "2. RECENT BIRTHDAY EMAIL ACTIVITY:\n";
    echo "----------------------------------\n";
    $stmt = $pdo->prepare('SELECT * FROM email_logs WHERE email_type LIKE "%birthday%" ORDER BY sent_at DESC LIMIT 5');
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($logs)) {
        echo "No recent birthday email logs found.\n\n";
    } else {
        foreach ($logs as $log) {
            echo "Date: {$log['sent_at']}\n";
            echo "Member ID: {$log['member_id']}\n";
            echo "Type: {$log['email_type']}\n";
            echo "Subject: {$log['subject']}\n";
            echo "Status: {$log['status']}\n";
            if (!empty($log['error_message'])) {
                echo "Error: {$log['error_message']}\n";
            }
            echo "\n";
        }
    }
    
    // 3. Check members with birthdays today
    echo "3. TODAY'S BIRTHDAYS:\n";
    echo "--------------------\n";
    $stmt = $pdo->prepare('SELECT id, full_name, email, image_path FROM members WHERE MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) = DAY(CURDATE())');
    $stmt->execute();
    $birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($birthdays)) {
        echo "No birthdays today.\n\n";
    } else {
        foreach ($birthdays as $member) {
            echo "Member: {$member['full_name']} ({$member['email']})\n";
            echo "Image: " . ($member['image_path'] ? $member['image_path'] : 'No image') . "\n";
            echo "\n";
        }
    }
    
    // 4. Check email settings
    echo "4. EMAIL CONFIGURATION:\n";
    echo "-----------------------\n";
    $stmt = $pdo->prepare('SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE "%email%" OR setting_key LIKE "%smtp%"');
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($settings as $setting) {
        echo "{$setting['setting_key']}: {$setting['setting_value']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== AUDIT COMPLETE ===\n";
?>

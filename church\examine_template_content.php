<?php
require_once 'config.php';

header('Content-Type: text/html');
echo "<h1>Birthday Template Content Analysis</h1>";

try {
    $stmt = $pdo->prepare('SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1 ORDER BY id');
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        echo "<h2>Template {$template['id']}: {$template['template_name']}</h2>";
        echo "<h3>Subject:</h3>";
        echo "<p>" . htmlspecialchars($template['subject']) . "</p>";
        
        echo "<h3>Content:</h3>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
        echo "<pre>" . htmlspecialchars($template['content']) . "</pre>";
        echo "</div>";
        
        echo "<h3>Rendered Preview:</h3>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo $template['content'];
        echo "</div>";
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>

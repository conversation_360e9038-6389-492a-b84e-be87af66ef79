<?php
/**
 * Test script to verify Email Throttling Settings functionality
 */

// Start session and include configuration
session_start();
require_once '../config.php';

// Test 1: Check if throttling settings can be retrieved
echo "<h2>Test 1: Retrieving Email Throttling Settings</h2>\n";

try {
    // Test delay setting
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds' LIMIT 1");
    $stmt->execute();
    $delay_result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($delay_result) {
        echo "✅ Email delay setting found: " . $delay_result['setting_value'] . " seconds<br>\n";
    } else {
        echo "❌ Email delay setting not found<br>\n";
    }
    
    // Test batch size setting
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size' LIMIT 1");
    $stmt->execute();
    $batch_result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($batch_result) {
        echo "✅ Email batch size setting found: " . $batch_result['setting_value'] . " emails<br>\n";
    } else {
        echo "❌ Email batch size setting not found<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error retrieving settings: " . $e->getMessage() . "<br>\n";
}

// Test 2: Test the helper functions from send_birthday_emails.php
echo "<h2>Test 2: Testing Helper Functions</h2>\n";

// Include the helper functions
require_once 'send_birthday_emails.php';

try {
    // Test get_email_batch_size function
    $batch_size = get_email_batch_size();
    echo "✅ get_email_batch_size() returned: $batch_size<br>\n";
    
    if ($batch_size >= 10 && $batch_size <= 5000) {
        echo "✅ Batch size is within valid range (10-5000)<br>\n";
    } else {
        echo "❌ Batch size is outside valid range<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing helper functions: " . $e->getMessage() . "<br>\n";
}

// Test 3: Test BirthdayReminder throttling methods
echo "<h2>Test 3: Testing BirthdayReminder Throttling Methods</h2>\n";

try {
    require_once '../send_birthday_reminders.php';
    
    $reminder = new BirthdayReminder($pdo);
    
    // Use reflection to test private methods
    $reflection = new ReflectionClass($reminder);
    
    // Test getEmailBatchSize method
    $getBatchSizeMethod = $reflection->getMethod('getEmailBatchSize');
    $getBatchSizeMethod->setAccessible(true);
    $batch_size = $getBatchSizeMethod->invoke($reminder);
    
    echo "✅ BirthdayReminder::getEmailBatchSize() returned: $batch_size<br>\n";
    
    if ($batch_size >= 10 && $batch_size <= 5000) {
        echo "✅ BirthdayReminder batch size is within valid range<br>\n";
    } else {
        echo "❌ BirthdayReminder batch size is outside valid range<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing BirthdayReminder methods: " . $e->getMessage() . "<br>\n";
}

// Test 4: Test settings update functionality
echo "<h2>Test 4: Testing Settings Update</h2>\n";

try {
    // Save current settings
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds'");
    $stmt->execute();
    $original_delay = $stmt->fetchColumn() ?: 3;
    
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size'");
    $stmt->execute();
    $original_batch = $stmt->fetchColumn() ?: 25;
    
    // Test update with new values
    $test_delay = 5;
    $test_batch = 50;
    
    // Update delay setting
    $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = ? WHERE setting_key = 'email_sending_delay_seconds'");
    $stmt->execute([$test_delay]);
    
    // Update batch setting
    $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = ? WHERE setting_key = 'email_batch_size'");
    $stmt->execute([$test_batch]);
    
    // Verify updates
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds'");
    $stmt->execute();
    $updated_delay = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size'");
    $stmt->execute();
    $updated_batch = $stmt->fetchColumn();
    
    if ($updated_delay == $test_delay && $updated_batch == $test_batch) {
        echo "✅ Settings update successful<br>\n";
        echo "✅ Delay updated to: $updated_delay seconds<br>\n";
        echo "✅ Batch size updated to: $updated_batch emails<br>\n";
    } else {
        echo "❌ Settings update failed<br>\n";
    }
    
    // Restore original settings
    $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = ? WHERE setting_key = 'email_sending_delay_seconds'");
    $stmt->execute([$original_delay]);
    
    $stmt = $pdo->prepare("UPDATE email_settings SET setting_value = ? WHERE setting_key = 'email_batch_size'");
    $stmt->execute([$original_batch]);
    
    echo "✅ Original settings restored<br>\n";
    
} catch (Exception $e) {
    echo "❌ Error testing settings update: " . $e->getMessage() . "<br>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "All tests completed. Check the results above for any issues.<br>\n";
echo "<a href='send_birthday_emails.php'>Go to Birthday Emails Page</a> | ";
echo "<a href='send_birthday_notification.php'>Go to Birthday Notifications Page</a><br>\n";
?>

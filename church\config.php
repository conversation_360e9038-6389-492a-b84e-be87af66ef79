<?php
// Check if the config file has already been included
if (defined('CONFIG_INCLUDED')) {
    return;
}
define('CONFIG_INCLUDED', true);

// Define environment based on server name
$environment = 'development'; // Default to development

if (isset($_SERVER['SERVER_NAME'])) {
    if (strpos($_SERVER['SERVER_NAME'], 'localhost') === false &&
        strpos($_SERVER['SERVER_NAME'], '127.0.0.1') === false) {
        $environment = 'production';
    } elseif (strpos($_SERVER['SERVER_NAME'], 'staging') !== false) {
        $environment = 'staging';
    }
}

// Configure session settings before any session is started
if (session_status() === PHP_SESSION_NONE) {
    // Simple session configuration for maximum compatibility
    ini_set('session.cookie_lifetime', 3600);
    ini_set('session.gc_maxlifetime', 3600);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0);
    ini_set('session.use_strict_mode', 0);
    ini_set('session.cookie_path', '/');
    ini_set('session.cookie_domain', '');
    ini_set('session.name', 'PHPSESSID');  // Use default session name
}

// Error reporting configuration based on environment
if ($environment === 'production') {
    // Production: Log errors but don't display them
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
    ini_set('display_errors', '0');
    ini_set('log_errors', '1');
    ini_set('error_log', __DIR__ . '/logs/php_errors.log');
} else {
    // Development/Staging: Show all errors for debugging
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
    ini_set('log_errors', '1');
    ini_set('error_log', __DIR__ . '/logs/php_errors.log');
}

// Ensure log directory exists
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Include environment configuration
$environment = require_once __DIR__ . '/environment.php';

// Define secure cron key for birthday reminders
if (!defined('SECURE_CRON_KEY')) {
    define('SECURE_CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');
}

// Ensure default profile image exists
if (!function_exists('ensure_default_profile_image')) {
    function ensure_default_profile_image() {
        $defaultImagePath = __DIR__ . '/assets/img/default-profile.jpg';
        
        // Check if the default image exists
        if (!file_exists($defaultImagePath)) {
            // Create the directory if it doesn't exist
            if (!is_dir(dirname($defaultImagePath))) {
                mkdir(dirname($defaultImagePath), 0755, true);
            }
            
            // Create a simple default profile image
            $width = 200;
            $height = 200;
            $image = imagecreatetruecolor($width, $height);
            
            // Set background color (light gray)
            $bgColor = imagecolorallocate($image, 240, 240, 240);
            imagefill($image, 0, 0, $bgColor);
            
            // Draw a circle for the head
            $circleColor = imagecolorallocate($image, 200, 200, 200);
            imagefilledellipse($image, $width/2, $height/2 - 15, 120, 120, $circleColor);
            
            // Draw a body shape
            imagefilledrectangle($image, $width/2 - 40, $height/2 + 40, $width/2 + 40, $height, $circleColor);
            
            // Save the image
            imagejpeg($image, $defaultImagePath, 90);
            imagedestroy($image);
            
            error_log("Created default profile image at: $defaultImagePath");
        }
        
        return file_exists($defaultImagePath);
    }
}

// Call the function to ensure the default image exists
ensure_default_profile_image();

// Database configuration
if ($environment === 'production') {
    // Production database settings
    $host = 'localhost';
    $dbname = 'u271750246_DDD';
    $username = 'u271750246_DDD';
    $password = 'Moremoney2025@!';
} elseif ($environment === 'staging') {
    // Staging database settings (using same as production for now, but can be changed)
    $host = 'localhost';
    $dbname = 'u271750246_DDD';
    $username = 'u271750246_DDD';
    $password = 'Moremoney2025@!';
} else {
    // Local XAMP development settings
    $host = 'localhost'; // XAMP default MySQL port
    $dbname = 'campaign'; // Always use churchtest for consistency
    $username = 'root';     // XAMP default username
    $password = '';     // XAMP default password
}

// Add a function to get a database connection consistently
if (!function_exists('getDbConnection')) {
    function getDbConnection() {
        global $pdo;
        
        // If we already have a connection, test it and reconnect if needed
        if ($pdo instanceof PDO) {
            try {
                $pdo->query("SELECT 1");
                return $pdo;
            } catch (PDOException $e) {
                error_log("Existing database connection failed, attempting to reconnect: " . $e->getMessage());
                // Connection is dead, we'll create a new one below
            }
        }
        
        // Get database settings from global scope
        global $host, $dbname, $username, $password;
        
        try {
            // Log connection attempt
            error_log("Getting new database connection: host=$host, dbname=$dbname, user=$username");
            
            // Create a new PDO instance with utf8mb4 charset and timeout settings
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 60, // Connection timeout in seconds
                PDO::MYSQL_ATTR_READ_TIMEOUT => 60, // Read timeout in seconds
                PDO::MYSQL_ATTR_WRITE_TIMEOUT => 60 // Write timeout in seconds
            ];
            
            // Create PDO instance
            $connection = new PDO($dsn, $username, $password, $options);
            
            // Set session variables for longer timeouts
            $connection->exec("SET SESSION wait_timeout=600"); // 10 minutes
            $connection->exec("SET SESSION interactive_timeout=600"); // 10 minutes
            
            // Test the connection
            $test = $connection->query("SELECT 1");
            if (!$test) {
                throw new Exception("Database connection test failed");
            }
            
            error_log("Database connection established successfully");
            return $connection;
            
        } catch (PDOException $e) {
            error_log("Database connection failed in getDbConnection(): " . $e->getMessage());
            throw $e; // Re-throw to allow caller to handle
        }
    }
}

// Define base URLs if not already defined in environment.php
if (!defined('BASE_URL') && defined('SITE_URL')) {
    define('BASE_URL', SITE_URL);
}

if (!defined('ADMIN_URL') && defined('SITE_URL')) {
    define('ADMIN_URL', SITE_URL . '/admin');
}

// Verify required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'fileinfo'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        error_log("Required PHP extension '$ext' is not loaded");
        die("Required PHP extension '$ext' is not loaded");
    }
}

try {
    // Log database connection attempt
    error_log("Attempting to connect to database: host=$host, dbname=$dbname, user=$username");
    
    // Set the MySQL connection charset to utf8mb4
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];

    // Create PDO instance
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // Test the connection
    $test = $pdo->query("SELECT 1");
    if (!$test) {
        throw new Exception("Database connection test failed");
    }
    
    error_log("Database connection successful");
    
    // Make PDO available globally
    global $pdo;

    // Load email settings from database
    $emailSettings = [];
    try {
        // First try to load from email_settings table (preferred)
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings");
        $stmt->execute();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $emailSettings[$row['setting_key']] = $row['setting_value'];
        }

        // If no settings found in email_settings table, try settings table
        if (empty($emailSettings)) {
            // First try with email_ prefix
            $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'email_%'");
            $stmt->execute();
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $key = str_replace('email_', '', $row['setting_key']);
                $emailSettings[$key] = $row['setting_value'];
            }

            // If still empty, try without prefix (direct mapping)
            if (empty($emailSettings)) {
                $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email')");
                $stmt->execute();
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $key = $row['setting_key'];
                    if ($key === 'smtp_encryption') {
                        $emailSettings['smtp_secure'] = $row['setting_value'];
                    } elseif ($key === 'from_email') {
                        $emailSettings['sender_email'] = $row['setting_value'];
                    } elseif ($key === 'from_name') {
                        $emailSettings['sender_name'] = $row['setting_value'];
                    } elseif ($key === 'reply_to_email') {
                        $emailSettings['reply_to_email'] = $row['setting_value'];
                        $emailSettings['replyToEmail'] = $row['setting_value']; // Alternative key format
                    } else {
                        $emailSettings[$key] = $row['setting_value'];
                    }
                    // Set smtp_auth to true by default
                    $emailSettings['smtp_auth'] = '1';
                }
            }
        }

        // Verify all required settings are present
        $required_settings = ['smtp_host', 'smtp_auth', 'smtp_username', 'smtp_password',
                             'smtp_secure', 'smtp_port', 'sender_email', 'sender_name'];
        $missing_settings = array_diff($required_settings, array_keys($emailSettings));

        if (!empty($missing_settings)) {
            error_log("Missing required email settings: " . implode(', ', $missing_settings));
            // Don't throw exception, just log the error and continue
            error_log("Available email settings: " . print_r($emailSettings, true));
        }
    } catch (PDOException $e) {
        error_log("Error loading email settings: " . $e->getMessage());
        // Don't throw exception, just log the error and continue
        error_log("Will attempt to use default email configuration");
    }
} catch(PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    if ($environment === 'production') {
        die("Database connection failed. Please contact support.");
    } else {
        die("Connection failed: " . $e->getMessage()); // Show detailed error for debugging
    }
}

// Include PHPMailer
require 'vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Base URL Configuration - now fully dynamic (no hardcoded paths)
// All URL constants are defined in environment.php using dynamic detection
// This ensures the application works on any domain/subdirectory structure

// Path helper functions
if (!function_exists('get_base_url')) {
    function get_base_url() {
        return BASE_URL;
    }
}

if (!function_exists('get_admin_url')) {
    function get_admin_url() {
        return ADMIN_URL;
    }
}

if (!function_exists('get_asset_url')) {
    function get_asset_url($path) {
        return BASE_URL . '/assets/' . ltrim($path, '/');
    }
}

if (!function_exists('get_admin_asset_url')) {
    function get_admin_asset_url($path) {
        return ADMIN_URL . '/assets/' . ltrim($path, '/');
    }
}

// URL helper function
if (!function_exists('url_for')) {
    function url_for($path) {
        return BASE_URL . '/' . ltrim($path, '/');
    }
}

if (!function_exists('admin_url_for')) {
    function admin_url_for($path) {
        return ADMIN_URL . '/' . ltrim($path, '/');
    }
}

// Helper function to fix file paths for public access
if (!function_exists('fix_file_path_for_public')) {
    function fix_file_path_for_public($path) {
        // Convert admin-relative paths (../uploads/) to public-relative paths
        if (strpos($path, '../uploads/') === 0) {
            // Check if we're in a subdirectory (like church/) by looking at the current script path
            $current_script = $_SERVER['SCRIPT_NAME'] ?? '';

            // If we're in the church directory, convert ../uploads/ to uploads/
            if (strpos($current_script, '/church/') !== false) {
                return str_replace('../uploads/', 'uploads/', $path);
            } else {
                // For root level pages, convert to uploads/
                return str_replace('../uploads/', 'uploads/', $path);
            }
        }
        return $path;
    }
}

// Helper function to fix file paths for user directory access
if (!function_exists('fix_file_path_for_user')) {
    function fix_file_path_for_user($path) {
        // From user directory, admin-relative paths should work as-is
        // since both admin/ and user/ are at the same level under church/
        return $path;
    }
}

// Helper function to convert number to ordinal (1st, 2nd, 3rd, etc.)
if (!function_exists('getOrdinal')) {
    function getOrdinal($number) {
        if (!is_numeric($number)) {
            return $number;
        }

        $number = (int)$number;
        $suffix = 'th';

        if ($number % 100 >= 11 && $number % 100 <= 13) {
            $suffix = 'th';
        } else {
            switch ($number % 10) {
                case 1:
                    $suffix = 'st';
                    break;
                case 2:
                    $suffix = 'nd';
                    break;
                case 3:
                    $suffix = 'rd';
                    break;
                default:
                    $suffix = 'th';
                    break;
            }
        }

        return $number . $suffix;
    }
}

// Handle email template placeholders
if (!function_exists('replaceTemplatePlaceholders')) {
    function replaceTemplatePlaceholders($content, $memberData, $skipMemberImage = false) {
        // Validate content
        if (empty($content)) {
            error_log("Warning: Empty content provided to replaceTemplatePlaceholders");
            return '';
        }
        
        // Ensure member data is an array
        if (!is_array($memberData)) {
            error_log("Warning: Member data is not an array in replaceTemplatePlaceholders");
            $memberData = [];
        }
        
        // Process member image URL and HTML
        $memberImageUrl = '';
        $memberImageHtml = '';
        if (!empty($memberData['image_path'])) {
            $imagePath = $memberData['image_path'];

            // Check if it's an absolute URL
            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                $memberImageUrl = $imagePath;
            } else {
                // Handle relative URLs - build full URL based on site URL
                $memberImageUrl = get_base_url() . '/' . ltrim($imagePath, '/');
            }

            // Create proper HTML for member image to match BirthdayReminder class behavior
            $memberImageHtml = '<img src="' . $memberImageUrl . '" alt="' .
                htmlspecialchars($memberData['full_name'] ?? 'Member') .
                '" style="display:block; width:200px; height:200px; border-radius:50%; margin:15px auto; object-fit:cover; border:4px solid #fff; box-shadow:0 4px 12px rgba(0,0,0,0.1);">';
        } else {
            // Use default profile image
            $memberImageUrl = get_base_url() . '/assets/img/default-avatar.png';

            // Create proper HTML for default image to match BirthdayReminder class behavior
            $memberImageHtml = '<img src="' . $memberImageUrl . '" alt="Default Profile" ' .
                'style="display:block; width:200px; height:200px; border-radius:50%; margin:15px auto; object-fit:cover; border:4px solid #fff; box-shadow:0 4px 12px rgba(0,0,0,0.1);">';
        }
        
        // Debug the member data
        error_log("Member data for template (first 3 items): " . 
                  (isset($memberData['full_name']) ? "full_name: " . $memberData['full_name'] : "no full_name") . ", " . 
                  (isset($memberData['email']) ? "email: " . $memberData['email'] : "no email") . ", " . 
                  (isset($memberData['birth_date']) ? "birth_date: " . $memberData['birth_date'] : "no birth_date"));
        
        // Ensure critical birthday fields are available
        if (!isset($memberData['upcoming_birthday_date']) && isset($memberData['birth_date'])) {
            // Calculate upcoming birthday
            $birth_month = date('m', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_day = date('d', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_year = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
            $current_year = date('Y');
            $next_year = $current_year + 1;
            
            // Determine if birthday has passed this year already
            $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
            $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;

            // Compare dates only (not time) to determine if birthday has passed
            $today_date = date('Y-m-d');
            $birthday_date = date('Y-m-d', strtotime($this_year_birthday));

            // Use next year's date only if this year's birthday has already passed (not today)
            $upcoming_date = $birthday_date < $today_date ? $next_year_birthday : $this_year_birthday;
            
            // Calculate age using consistent DateTime method
            // For birthday notifications, calculate the age they will be on their birthday
            try {
                $birth = new DateTime($memberData['birth_date']);
                $today = new DateTime();

                // Check if this is for a birthday notification by looking for explicit flag or birthday member data
                $isBirthdayNotification = isset($memberData['_is_birthday_notification']) ||
                                        isset($memberData['birthday_member_name']) ||
                                        isset($memberData['birthday_member_full_name']) ||
                                        isset($memberData['birthday_member_age']);

                if ($isBirthdayNotification) {
                    // For birthday notifications, calculate age they will be on their upcoming birthday
                    $birthMonth = $birth->format('m');
                    $birthDay = $birth->format('d');
                    $currentYear = $today->format('Y');

                    // Create this year's birthday date
                    $thisYearBirthday = new DateTime("$currentYear-$birthMonth-$birthDay");

                    // If birthday has already passed this year, use next year's birthday
                    if ($thisYearBirthday < $today) {
                        $thisYearBirthday->modify('+1 year');
                    }

                    // Calculate age they will be on their upcoming birthday
                    $age = $birth->diff($thisYearBirthday)->y;
                    error_log("Birthday notification: calculated birthday age $age for birth date " . $memberData['birth_date']);
                } else {
                    // For regular templates, use current age
                    $age = $today->diff($birth)->y;
                    error_log("Regular template: calculated current age $age for birth date " . $memberData['birth_date']);
                }
            } catch (Exception $e) {
                error_log("Error calculating age for birth date: " . $memberData['birth_date'] . " - " . $e->getMessage());
                $age = 'Unknown';
            }
            
            // Add days until birthday
            $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
            
            // Add critical birthday values to the data array
            $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
            $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
            $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
            $memberData['days_until_birthday'] = $days_until;
            $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                      ($days_until == 1 ? 'tomorrow' : 
                                      "in $days_until days");
            $memberData['age'] = $age;
            $memberData['birthday_member_age'] = $age;

            // Add celebration context for better grammar
            $memberData['celebration_text'] = $days_until == 0 ? 'celebrating today' :
                                            ($days_until == 1 ? 'celebrating tomorrow' :
                                            "celebrating in $days_until days");

            $memberData['birthday_context'] = $days_until == 0 ? 'on this special day' :
                                            ($days_until == 1 ? 'tomorrow' :
                                            "in $days_until days");

            // Add ordinal age
            $memberData['age_ordinal'] = getOrdinal($age);
            $memberData['birthday_member_age_ordinal'] = getOrdinal($age);

            // Ensure birthday member placeholders are set
            if (empty($memberData['birthday_member_full_name'])) {
                $memberData['birthday_member_full_name'] = $memberData['full_name'] ?? 'Member';
            }

            if (empty($memberData['birthday_member_name'])) {
                $memberData['birthday_member_name'] = $memberData['first_name'] ?? explode(' ', $memberData['birthday_member_full_name'])[0] ?? 'Member';
            }

            if (empty($memberData['birthday_member_email'])) {
                $memberData['birthday_member_email'] = $memberData['email'] ?? '';
            }

            // Log autocreated placeholders
            error_log("Auto-created birthday placeholders: upcoming_birthday_date=" . $memberData['upcoming_birthday_date'] .
                      ", days_text=" . $memberData['days_text'] . ", age=" . $memberData['age'] .
                      ", birthday_member_name=" . $memberData['birthday_member_name']);
        }
        
        // Ensure first_name if we have full_name
        if (!isset($memberData['first_name']) && isset($memberData['full_name'])) {
            $nameParts = explode(' ', $memberData['full_name'], 2);
            $memberData['first_name'] = $nameParts[0];
            $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
        }
        
        $placeholders = [
            // Common name placeholders (most frequently used)
            '{full_name}' => $memberData['full_name'] ?? '',
            '{name}' => $memberData['first_name'] ?? '',
            '{first_name}' => $memberData['first_name'] ?? '',
            '{last_name}' => $memberData['last_name'] ?? '',

            // General member placeholders
            '{member_name}' => $memberData['first_name'] ?? '',
            '{member_full_name}' => ($memberData['first_name'] ?? '') . ' ' . ($memberData['last_name'] ?? ''),
            '{member_email}' => $memberData['email'] ?? '',
            '{member_phone}' => $memberData['phone_number'] ?? $memberData['phone'] ?? '',
            '{member_address}' => $memberData['home_address'] ?? $memberData['address'] ?? '',
            '{member_city}' => $memberData['city'] ?? '',
            '{member_state}' => $memberData['state'] ?? '',
            '{member_zip}' => $memberData['zip'] ?? '',

            // Birthday-specific placeholders
            '{birthday_date}' => $memberData['birthday_date'] ?? date('F j'),
            '{birthday_year}' => $memberData['birthday_year'] ?? date('Y'),
            '{current_year}' => date('Y'),
            '{current_date}' => date('F j, Y'),
            '{days_text}' => $memberData['days_text'] ?? 'today',
            '{days_until_birthday}' => $memberData['days_until_birthday'] ?? 0,
            '{member_country}' => $memberData['country'] ?? '',
            '{member_birth_date}' => $memberData['birth_date'] ?? '',
            '{member_join_date}' => $memberData['join_date'] ?? '',
            
            // Direct field access - map the direct field names that come from registration
            '{email}' => $memberData['email'] ?? '',
            '{phone_number}' => $memberData['phone_number'] ?? '',
            '{phone}' => $memberData['phone_number'] ?? $memberData['phone'] ?? '',
            '{home_address}' => $memberData['home_address'] ?? '',
            '{address}' => $memberData['home_address'] ?? $memberData['address'] ?? '',
            '{occupation}' => $memberData['occupation'] ?? '',
            
            // Member image placeholders
            '{member_image}' => $skipMemberImage ? '' : $memberImageUrl, // Use URL - email processing will handle HTML
            '{member_image_url}' => $memberImageUrl,
            '{image_path}' => $memberImageUrl,
            '{profile_photo}' => $skipMemberImage ? '' : $memberImageUrl, // Use URL - email processing will handle HTML
            
            // Bulk email specific placeholders
            '{recipient_full_name}' => $memberData['recipient_full_name'] ?? $memberData['full_name'] ?? '',
            '{recipient_first_name}' => $memberData['recipient_first_name'] ?? (isset($memberData['full_name']) ? explode(' ', $memberData['full_name'])[0] : ''),
            '{recipient_email}' => $memberData['recipient_email'] ?? $memberData['email'] ?? '',
            '{recipient_phone}' => $memberData['recipient_phone'] ?? $memberData['phone_number'] ?? $memberData['phone'] ?? '',
            '{sender_name}' => $memberData['sender_name'] ?? '',
            '{sender_email}' => $memberData['sender_email'] ?? '',
            '{total_recipients}' => $memberData['total_recipients'] ?? '',
            
            // Organization information (organization-agnostic)
            '{church_name}' => get_site_setting('organization_name', get_site_setting('site_title', 'Organization')),
            '{organization_name}' => get_site_setting('organization_name', get_site_setting('site_title', 'Organization')),
            '{organization_type}' => get_site_setting('organization_type', 'organization'),
            '{organization_mission}' => get_site_setting('organization_mission', ''),
            '{organization_vision}' => get_site_setting('organization_vision', ''),
            '{organization_values}' => get_site_setting('organization_values', ''),
            '{church_address}' => get_site_setting('contact_address', ''),
            '{organization_address}' => get_site_setting('contact_address', ''),
            '{church_phone}' => get_site_setting('contact_phone', ''),
            '{organization_phone}' => get_site_setting('contact_phone', ''),
            '{church_email}' => get_site_setting('contact_email', ''),
            '{organization_email}' => get_site_setting('contact_email', ''),
            '{church_website}' => get_site_setting('website_url', ''),
            '{organization_website}' => get_site_setting('website_url', ''),
            '{church_logo}' => get_base_url() . '/assets/images/banner.jpg',
            '{site_url}' => get_base_url(),

            // Dynamic terminology based on organization type
            '{member_term}' => get_site_setting('member_term', 'Member'),
            '{leader_term}' => get_site_setting('leader_term', 'Leader'),
            '{group_term}' => get_site_setting('group_term', 'Group'),
            '{event_term}' => get_site_setting('event_term', 'Event'),
            '{donation_term}' => get_site_setting('donation_term', 'Donation'),
            
            // Date and time placeholders
            '{current_year}' => date('Y'),
            '{current_date}' => date('F j, Y'),
            '{current_time}' => date('g:i A'),
            
            // Birthday-specific placeholders
            '{birthday_date}' => $memberData['birthday_date'] ?? '',
            '{birthday_year}' => $memberData['birthday_year'] ?? '',
            '{upcoming_birthday_date}' => $memberData['upcoming_birthday_date'] ?? '',
            '{upcoming_birthday_day}' => $memberData['upcoming_birthday_day'] ?? '',
            '{upcoming_birthday_formatted}' => $memberData['upcoming_birthday_formatted'] ?? '',
            '{days_until_birthday}' => $memberData['days_until_birthday'] ?? '',
            '{days_text}' => $memberData['days_text'] ?? '',
            '{celebration_text}' => $memberData['celebration_text'] ?? '',
            '{birthday_context}' => $memberData['birthday_context'] ?? '',
            
            // Birthday member-specific placeholders
            '{birthday_member_name}' => $memberData['birthday_member_name'] ?? '',
            '{birthday_member_first_name}' => $memberData['birthday_member_first_name'] ?? $memberData['birthday_member_name'] ?? '',
            '{birthday_member_full_name}' => $memberData['birthday_member_full_name'] ?? '',
            '{birthday_member_email}' => $memberData['birthday_member_email'] ?? '',
            '{birthday_member_phone}' => $memberData['birthday_member_phone'] ?? '',
            '{birthday_member_image}' => $skipMemberImage ? '' : (isset($memberData['birthday_member_image']) ? $memberData['birthday_member_image'] : $memberImageHtml), // Use HTML for preview
            '{birthday_member_photo_url}' => $memberData['birthday_member_photo_url'] ?? $memberImageUrl,
            '{birthday_member_birth_date}' => $memberData['birthday_member_birth_date'] ?? '',
            '{birthday_member_age}' => $memberData['birthday_member_age'] ?? '',
            '{age}' => $memberData['age'] ?? $memberData['birthday_member_age'] ?? '',
            '{age_ordinal}' => $memberData['age_ordinal'] ?? '',
            '{birthday_member_age_ordinal}' => $memberData['birthday_member_age_ordinal'] ?? '',

            // Add member_image placeholder with HTML version
            '{member_image}' => $skipMemberImage ? '' : (isset($memberData['member_image']) ? $memberData['member_image'] : $memberImageHtml),
            '{member_image_url}' => $memberData['member_image_url'] ?? $memberImageUrl,
            '{image_path}' => $memberData['image_path'] ?? '',
            '{profile_photo}' => $skipMemberImage ? '' : (isset($memberData['profile_photo']) ? $memberData['profile_photo'] : $memberImageHtml),

            // Event-specific placeholders
            '{event_title}' => $memberData['event_title'] ?? '',
            '{event_date}' => $memberData['event_date'] ?? '',
            '{event_time}' => $memberData['event_time'] ?? '',
            '{event_location}' => $memberData['event_location'] ?? '',
            '{event_description}' => $memberData['event_description'] ?? '',
            '{event_description_section}' => $memberData['event_description_section'] ?? '',

            // Other placeholders
            '{unsubscribe_link}' => url_for('unsubscribe.php?email=' . urlencode($memberData['email'] ?? '') . '&token=' . md5($memberData['email'] ?? '')),
        ];
        
        // Replace all placeholders
        $result = str_replace(array_keys($placeholders), array_values($placeholders), $content);
        
        // Debug the result
        error_log("Template after replacement: " . substr($result, 0, 200) . "...");
        
        return $result;
    }
}

// Additional shortcode functions for production readiness testing
if (!function_exists('process_shortcodes')) {
    function process_shortcodes($content, $data = []) {
        return replaceTemplatePlaceholders($content, $data);
    }
}

if (!function_exists('get_member_shortcodes')) {
    function get_member_shortcodes() {
        return [
            'member_info' => [
                '{full_name}', '{first_name}', '{last_name}', '{email}', '{phone_number}',
                '{home_address}', '{occupation}', '{member_image}', '{birth_date}'
            ],
            'organization' => [
                '{church_name}', '{organization_name}', '{church_address}', '{church_phone}',
                '{church_email}', '{church_logo}', '{site_url}'
            ],
            'birthday' => [
                '{birthday_member_name}', '{birthday_member_full_name}', '{birthday_member_photo_url}',
                '{birthday_member_age}', '{days_until_birthday}', '{days_text}', '{upcoming_birthday_formatted}'
            ],
            'recipient' => [
                '{recipient_full_name}', '{recipient_first_name}', '{recipient_email}',
                '{sender_name}', '{sender_email}', '{total_recipients}'
            ],
            'datetime' => [
                '{current_year}', '{current_date}', '{current_time}'
            ],
            'event' => [
                '{event_title}', '{event_date}', '{event_time}', '{event_location}', '{event_description}'
            ]
        ];
    }
}

// Global variable to track email errors
$last_email_error = null;

// Send email function
if (!function_exists('sendEmail')) {
    function sendEmail($to, $toName, $subject, $body, $isHTML = true, $memberData = null) {
        global $last_email_error, $emailSettings;
        $last_email_error = null;
        
        // Create a custom error log file for debugging
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Global sendEmail called for $toName <$to>\n", FILE_APPEND);
        
        // Set script timeout
        set_time_limit(180); // 3 minutes timeout for email processing
        
        // Verify email settings are available
        if (empty($emailSettings) || !isset($emailSettings['smtp_host'])) {
            $error = "Email settings not properly configured";
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Error: $error\n", FILE_APPEND);
            if (!empty($emailSettings)) {
                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Available email settings: " . print_r($emailSettings, true) . "\n", FILE_APPEND);
            }
            $last_email_error = $error;
            return false;
        }
        
        // Validate inputs
        if (empty($to) || !filter_var($to, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid recipient email address: " . htmlspecialchars($to);
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Error: $error\n", FILE_APPEND);
            $last_email_error = $error;
            return false;
        }
        
        if (empty($subject)) {
            $error = "Email subject cannot be empty";
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Error: $error\n", FILE_APPEND);
            $last_email_error = $error;
            return false;
        }
        
        if (empty($body)) {
            $error = "Email body cannot be empty";
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Error: $error\n", FILE_APPEND);
            $last_email_error = $error;
            return false;
        }
        
        // Check if this is a newsletter
        $isNewsletter = false;
        if (isset($memberData['template_name']) && stripos($memberData['template_name'], 'newsletter') !== false) {
            $isNewsletter = true;
        }
        
        // Also check the subject for newsletter
        if (stripos($subject, 'newsletter') !== false) {
            $isNewsletter = true;
        }
        
        // Check custom_subject for newsletter
        if (isset($memberData['custom_subject']) && stripos($memberData['custom_subject'], 'newsletter') !== false) {
            $isNewsletter = true;
        }
        
        // Check if this is a birthday notification or birthday template
        $isBirthdayNotification = false;

        // Method 1: Check explicit flag (most reliable)
        if (isset($memberData['_is_birthday_notification']) && $memberData['_is_birthday_notification']) {
            $isBirthdayNotification = true;
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Birthday notification detected via explicit flag\n", FILE_APPEND);
        }
        // Method 2: Check template name
        else if (isset($memberData['template_name']) &&
            (stripos($memberData['template_name'], 'birthday notification') !== false ||
             stripos($memberData['template_name'], 'birthday template') !== false ||
             stripos($memberData['template_name'], 'birthday reminder') !== false)) {
            $isBirthdayNotification = true;
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Birthday notification detected via template name: " . $memberData['template_name'] . "\n", FILE_APPEND);
        }
        // Method 3: Check for birthday member data (fallback)
        else if (isset($memberData['birthday_member_name']) ||
                 isset($memberData['birthday_member_full_name']) ||
                 isset($memberData['birthday_member_age']) ||
                 isset($memberData['birthday_member_photo_url'])) {
            $isBirthdayNotification = true;
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Birthday notification detected via birthday member data\n", FILE_APPEND);
        }
        
        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Email type: " . 
            ($isNewsletter ? 'Newsletter' : ($isBirthdayNotification ? 'Birthday Notification' : 'Regular')) . "\n", FILE_APPEND);
        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] SMTP settings: Host={$emailSettings['smtp_host']}, Port={$emailSettings['smtp_port']}, Username={$emailSettings['smtp_username']}, Secure={$emailSettings['smtp_secure']}\n", FILE_APPEND);
        
        $mail = new PHPMailer(true);
        try {
            // Configure SMTP debugging based on environment
            $environment = get_site_setting('environment', 'development');
            if ($environment === 'production') {
                $mail->SMTPDebug = 0;  // No debugging in production
            } else {
                $mail->SMTPDebug = 2;  // Enable debugging in development/staging
                $mail->Debugoutput = function($str, $level) use ($debug_log_file) {
                    file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] SMTP Debug [$level]: $str\n", FILE_APPEND);
                };
            }
            
            $mail->isSMTP();
            $mail->Host = $emailSettings['smtp_host'];
            $mail->SMTPAuth = filter_var($emailSettings['smtp_auth'], FILTER_VALIDATE_BOOLEAN);
            $mail->Username = $emailSettings['smtp_username'];
            $mail->Password = $emailSettings['smtp_password'];
            // Fix port/encryption mismatch - Port 465 requires SSL, Port 587 uses TLS
            $port = intval($emailSettings['smtp_port']);
            $security = strtolower($emailSettings['smtp_secure']);

            if ($port == 465) {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;  // SSL for port 465
                $mail->Port = 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;  // TLS for port 587
                $mail->Port = 587;
            }
            $mail->setFrom($emailSettings['sender_email'], $emailSettings['sender_name']);

            // Set Reply-To if configured
            if (!empty($emailSettings['reply_to_email'])) {
                $mail->addReplyTo($emailSettings['reply_to_email'], $emailSettings['sender_name']);
                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Set Reply-To address: {$emailSettings['reply_to_email']}\n", FILE_APPEND);
            }
            
            // Set timeouts
            $mail->Timeout = 30;     // Reduced SMTP connection timeout
            $mail->SMTPKeepAlive = true; // Keep connection alive for multiple emails

            // Set SSL/TLS verification options for problem servers
            $mail->SMTPOptions = [
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true,
                    'cafile' => false,
                    'capath' => false,
                    'ciphers' => 'DEFAULT:!DH'
                ]
            ];
            
            $mail->addAddress($to, $toName);
            $mail->isHTML($isHTML);
            $mail->Subject = $subject;
            
            // Process and embed images in the email body
            if ($isHTML && $memberData && !$isNewsletter) {
                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Processing HTML email with images\n", FILE_APPEND);

                // Check if this is a birthday notification email with birthday member photo
                if ($isBirthdayNotification && isset($memberData['birthday_member_photo_url'])) {
                    $birthdayPhotoUrl = $memberData['birthday_member_photo_url'];
                    file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Found birthday member photo URL: $birthdayPhotoUrl - embedding inline to display properly\n", FILE_APPEND);

                    // SECURITY: Verify this image belongs to the correct member
                    $expectedMemberEmail = $to;
                    file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] SECURITY CHECK: Email recipient: $expectedMemberEmail\n", FILE_APPEND);
                    if (isset($memberData['email'])) {
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] SECURITY CHECK: Member data email: {$memberData['email']}\n", FILE_APPEND);
                    }

                    // Method 1: Try to use original image path if available
                    $localPath = null;
                    if (isset($memberData['_original_image_path']) && !empty($memberData['_original_image_path'])) {
                        $localPath = __DIR__ . '/' . $memberData['_original_image_path'];
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Using original image path: {$memberData['_original_image_path']}\n", FILE_APPEND);
                    } else {
                        // Method 2: Convert URL to local path
                        $siteUrl = defined('SITE_URL') ? SITE_URL : '';
                        $imagePath = str_replace($siteUrl . '/', '', $birthdayPhotoUrl);
                        $localPath = __DIR__ . '/' . $imagePath;
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Converting URL to path: $imagePath\n", FILE_APPEND);
                    }

                    // Clean up the path
                    $localPath = str_replace('\\', '/', $localPath);
                    $localPath = preg_replace('#/+#', '/', $localPath);

                    if (file_exists($localPath)) {
                        // Get image filename and mime type
                        $filename = basename($localPath);
                        $mime = mime_content_type($localPath) ?: 'image/jpeg';

                        // Create a unique CID for this specific email to prevent cross-contamination
                        $uniqueCid = 'birthday_member_image_' . md5($to . $localPath . time());

                        // Add the embedded image with explicit inline disposition
                        $mail->addEmbeddedImage($localPath, $uniqueCid, $filename, 'base64', $mime, 'inline');

                        // Replace all instances of the birthday member photo URL with the unique CID
                        $body = str_replace($birthdayPhotoUrl, 'cid:' . $uniqueCid, $body);

                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Embedded birthday member image: $localPath as CID: $uniqueCid\n", FILE_APPEND);

                        // Log success to a separate file for easier debugging
                        $image_log_file = __DIR__ . '/logs/birthday_image_embedding.log';
                        file_put_contents($image_log_file, "[" . date('Y-m-d H:i:s') . "] SUCCESS: Embedded birthday member image for email to: $to\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Image path: $localPath\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Unique CID: $uniqueCid\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - MIME: $mime\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Original URL: $birthdayPhotoUrl\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Detection method: " . (isset($memberData['_is_birthday_notification']) ? "Explicit flag" : "Template detection") . "\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Original image path: " . (isset($memberData['_original_image_path']) ? $memberData['_original_image_path'] : "Not provided") . "\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Recipient email: $to\n", FILE_APPEND);
                        file_put_contents($image_log_file, "---------------------------------------------------\n", FILE_APPEND);
                    } else {
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Birthday member image not found: $localPath\n", FILE_APPEND);

                        // Log failure to a separate file for easier debugging
                        $image_log_file = __DIR__ . '/logs/birthday_image_embedding.log';
                        file_put_contents($image_log_file, "[" . date('Y-m-d H:i:s') . "] FAILURE: Could not embed birthday member image for email to: $to\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Image path not found: $localPath\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Original URL: $birthdayPhotoUrl\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Detection method: " . (isset($memberData['_is_birthday_notification']) ? "Explicit flag" : "Template detection") . "\n", FILE_APPEND);
                        file_put_contents($image_log_file, "  - Original image path: " . (isset($memberData['_original_image_path']) ? $memberData['_original_image_path'] : "Not provided") . "\n", FILE_APPEND);
                        file_put_contents($image_log_file, "---------------------------------------------------\n", FILE_APPEND);
                    }
                }

                // For non-birthday notifications, process images normally
                else if (!$isBirthdayNotification) {
                    // First, check if we have a member profile image to embed
                    if (isset($memberData['_original_image_path']) && !empty($memberData['_original_image_path'])) {
                        $imagePath = $memberData['_original_image_path'];
                        $localPath = __DIR__ . '/' . $imagePath;

                        // Clean up the path
                        $localPath = str_replace('\\', '/', $localPath);
                        $localPath = preg_replace('#/+#', '/', $localPath);

                        if (file_exists($localPath)) {
                            // Get image filename and mime type
                            $filename = basename($localPath);
                            $mime = mime_content_type($localPath) ?: 'image/jpeg';

                            // Add the embedded image with explicit inline disposition
                            $mail->addEmbeddedImage($localPath, 'profile_image', $filename, 'base64', $mime, 'inline');

                            // Replace all instances of the profile image URL with the CID
                            $siteUrl = defined('SITE_URL') ? SITE_URL : '';
                            $imageUrl = $siteUrl . '/' . ltrim($imagePath, '/');
                            $body = str_replace($imageUrl, 'cid:profile_image', $body);

                            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Embedded profile image: $localPath as CID: profile_image\n", FILE_APPEND);
                        } else {
                            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Profile image not found: $localPath\n", FILE_APPEND);
                        }
                    }
                }

                // Extract and embed other images from HTML content (for all email types)
                // This will process any remaining images that weren't handled above
                {
                    // Extract image paths from the HTML content using a regex for img tags
                    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $body, $matches);

                    if (!empty($matches[1])) {
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Found " . count($matches[1]) . " images in email content\n", FILE_APPEND);

                        $imageCounter = 0;
                        foreach ($matches[1] as $imgSrc) {
                            // Skip tracking pixels, embedded images, external images, and unwanted images
                            if (strpos($imgSrc, 'track.php') !== false ||
                                strpos($imgSrc, 'data:image') === 0 ||
                                strpos($imgSrc, 'cid:') === 0 ||
                                strpos($imgSrc, 'receipt') !== false ||  // Skip receipt images
                                strpos($imgSrc, 'payment') !== false ||  // Skip payment images
                                strpos($imgSrc, 'invoice') !== false ||  // Skip invoice images
                                (strpos($imgSrc, 'http://') === 0 && strpos($imgSrc, SITE_URL) === false) ||
                                (strpos($imgSrc, 'https://') === 0 && strpos($imgSrc, SITE_URL) === false)) {
                                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Skipping image: $imgSrc\n", FILE_APPEND);
                                continue;
                            }

                            // Convert URL to local file path
                            $localPath = null;

                            if (strpos($imgSrc, SITE_URL) === 0) {
                                // Remove the site URL to get the relative path
                                $relativePath = str_replace(SITE_URL, '', $imgSrc);
                                $relativePath = ltrim($relativePath, '/');
                                $localPath = __DIR__ . '/' . $relativePath;
                            } else if (strpos($imgSrc, '/') === 0) {
                                // Absolute path from web root
                                $localPath = __DIR__ . $imgSrc;
                            } else {
                                // Relative path
                                $localPath = __DIR__ . '/' . $imgSrc;
                            }

                            // Clean up the path
                            $localPath = str_replace('\\', '/', $localPath);
                            $localPath = preg_replace('#/+#', '/', $localPath);

                            // Check if file exists
                            if (file_exists($localPath)) {
                                $imageCounter++;
                                // Create unique CID to prevent cross-contamination between emails
                                $cid = 'img' . $imageCounter . '_' . md5($to . $localPath . time());

                                // Get image filename and mime type
                                $filename = basename($localPath);
                                $mime = mime_content_type($localPath) ?: 'image/jpeg';

                                // Add the embedded image with explicit inline disposition
                                $mail->addEmbeddedImage($localPath, $cid, $filename, 'base64', $mime, 'inline');

                                // Replace the src in the HTML
                                $body = str_replace($imgSrc, 'cid:' . $cid, $body);

                                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Embedded image: $localPath as CID: $cid for recipient: $to\n", FILE_APPEND);
                            } else {
                                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Image file not found: $localPath (from URL: $imgSrc)\n", FILE_APPEND);
                            }
                        }
                    }
                }
            } else if ($isNewsletter) {
                file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Newsletter detected - skipping image embedding\n", FILE_APPEND);
                
                // For newsletters, remove any img tags with member_image, profile_image, or image_path
                $body = preg_replace('/<img[^>]+src=["\'][^"\']*(?:member_image|profile_image|image_path)[^"\']*["\'][^>]*>/i', '', $body);
                
                // Remove common image container divs
                $body = preg_replace('/<div[^>]*class=["\'][^"\']*(?:member-image|profile-image)[^"\']*["\'][^>]*>.*?<\/div>/is', '', $body);
                
                // Remove any remaining image containers
                $body = preg_replace('/<div[^>]*class=["\'][^"\']*(?:image-container|avatar|photo)[^"\']*["\'][^>]*>.*?<\/div>/is', '', $body);
            }
            
            $mail->Body = $body;
            
            // Log email attempt
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Attempting to send email to: $to with subject: $subject\n", FILE_APPEND);
            
            // Send the email with retry logic
            $maxRetries = 2;
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    if ($mail->send()) {
                        file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Email sent successfully to $to\n", FILE_APPEND);
                        return true;
                    } else {
                        throw new Exception($mail->ErrorInfo);
                    }
                } catch (Exception $sendException) {
                    $retryCount++;
                    file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] Email send attempt $retryCount failed: " . $sendException->getMessage() . "\n", FILE_APPEND);

                    if ($retryCount >= $maxRetries) {
                        throw $sendException; // Re-throw the exception after all retries
                    } else {
                        sleep(1); // Wait 1 second before retry
                    }
                }
            }
            
        } catch (Exception $e) {
            $errorMsg = "Email sending failed: " . $e->getMessage();
            file_put_contents($debug_log_file, "[" . date('Y-m-d H:i:s') . "] $errorMsg\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
            $last_email_error = $errorMsg;
            return false;
        }
    }
}

// Log database operations for sensitive data
if (!function_exists('log_database_operation')) {
    function log_database_operation($operation, $table, $data = [], $user_id = null) {
        global $pdo;
        
        if (!isset($pdo) || !$pdo) {
            error_log("Database connection not available for logging operation");
            return false;
        }
        
        try {
            $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, operation_type, table_affected, data_summary, ip_address) VALUES (?, ?, ?, ?, ?)");
            
            // Get user ID from session if not provided
            if ($user_id === null && isset($_SESSION['admin_id'])) {
                $user_id = $_SESSION['admin_id'];
            }
            
            // Summarize data for logging (don't log sensitive data in full)
            $data_summary = '';
            if (!empty($data)) {
                if (is_array($data)) {
                    // Create a summary without sensitive values
                    $safe_data = [];
                    foreach ($data as $key => $value) {
                        if (in_array(strtolower($key), ['password', 'token', 'secret', 'credit_card'])) {
                            $safe_data[$key] = '[REDACTED]';
                        } else {
                            $safe_data[$key] = (is_string($value) && strlen($value) > 50) ? 
                                substr($value, 0, 50) . '...' : $value;
                        }
                    }
                    $data_summary = json_encode($safe_data);
                } else {
                    $data_summary = (string)$data;
                }
            }
            
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            
            $stmt->execute([$user_id, $operation, $table, $data_summary, $ip_address]);
            return true;
        } catch (Exception $e) {
            error_log("Failed to log database operation: " . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('sendWelcomeEmail')) {
    function sendWelcomeEmail($memberData) {
        // Get the welcome email template from the database
        global $pdo;
        
        // Validate member data
        if (!isset($memberData['email']) || empty($memberData['email'])) {
            error_log("Cannot send welcome email: No email address provided");
            return false;
        }
        
        try {
            // First check if we have automated email settings for welcome emails
            $stmt = $pdo->prepare("
                SELECT template_ids 
                FROM automated_emails_settings 
                WHERE email_type = 'welcome'
                LIMIT 1
            ");
            $stmt->execute();
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            error_log("Welcome email settings: " . print_r($settings, true));
            error_log("Member data for welcome email: " . print_r($memberData, true));
            
            if ($settings && !empty($settings['template_ids'])) {
                // We have automated settings, use one of the templates from the list
                $template_ids = explode(',', $settings['template_ids']);
                // Select a random ID from the configured templates
                $selected_id = $template_ids[array_rand($template_ids)];
                
                error_log("Selected welcome template ID: " . $selected_id);
                
                $stmt = $pdo->prepare("
                    SELECT id, template_name, subject, content 
                    FROM email_templates 
                    WHERE id = ?
                ");
                $stmt->execute([$selected_id]);
                $template = $stmt->fetch(PDO::FETCH_ASSOC);
                
                error_log("Using template: " . ($template ? $template['template_name'] : 'Template not found'));
            } else {
                // Fallback to the old method if no automated settings
                $stmt = $pdo->prepare("
                    SELECT id, template_name, subject, content 
                    FROM email_templates 
                    WHERE template_name LIKE '%welcome%' 
                    ORDER BY RAND() 
                    LIMIT 1
                ");
                $stmt->execute();
                $template = $stmt->fetch(PDO::FETCH_ASSOC);
                
                error_log("Using random welcome template: " . ($template ? $template['template_name'] : 'No welcome template found'));
            }
            
            if (!$template) {
                // Use default template if none found - organization-agnostic
                $organizationName = get_site_setting('organization_name', get_site_setting('site_title', 'Organization'));
                $organizationType = get_site_setting('organization_type', 'organization');
                $memberTerm = get_site_setting('member_term', 'Member');

                // Dynamic greeting based on organization type
                $welcomeGreeting = "Welcome to our " . ucfirst($organizationType) . "!";
                if ($organizationType === 'church') {
                    $welcomeGreeting = "Welcome to our Church Family!";
                } elseif ($organizationType === 'school') {
                    $welcomeGreeting = "Welcome to our School Community!";
                } elseif ($organizationType === 'business') {
                    $welcomeGreeting = "Welcome to our Team!";
                } elseif ($organizationType === 'nonprofit') {
                    $welcomeGreeting = "Welcome to our Organization!";
                }

                $subject = "Welcome to " . $organizationName;
                $content = "
                    <html>
                    <body>
                        <h2>$welcomeGreeting</h2>
                        <p>Dear {member_name},</p>
                        <p>We are delighted to welcome you to our community. Thank you for registering with us.</p>
                        <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>
                        <p>Best regards,<br>The " . $organizationName . " Team</p>
                    </body>
                    </html>
                ";
                error_log("Using default welcome template as no template was found in the database");
            } else {
                $subject = $template['subject'];
                $content = $template['content'];
                error_log("Template content length: " . strlen($content));
            }
            
            // Process the template content with the member data
            $processed_subject = replaceTemplatePlaceholders($subject, $memberData);
            $processed_content = replaceTemplatePlaceholders($content, $memberData);
            
            error_log("Processed subject: " . $processed_subject);
            error_log("Sending welcome email to: " . $memberData['email'] . " with subject: " . $processed_subject);
            
            // Send the welcome email
            return sendEmail(
                $memberData['email'],
                $memberData['first_name'] . ' ' . $memberData['last_name'],
                $processed_subject,
                $processed_content,
                true,
                $memberData
            );
        } catch (Exception $e) {
            error_log("Error sending welcome email: " . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('getRandomTemplateForType')) {
    function getRandomTemplateForType($type) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("
                SELECT id, template_name, subject, content 
                FROM email_templates 
                WHERE template_name LIKE ? 
                ORDER BY RAND() 
                LIMIT 1
            ");
            $stmt->execute(['%' . $type . '%']);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting random template: " . $e->getMessage());
            return null;
        }
    }
}

// Function to get a site setting
if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = '') {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ? LIMIT 1");
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result && isset($result['setting_value'])) {
                return $result['setting_value'];
            }
        } catch (PDOException $e) {
            error_log("Error getting site setting: " . $e->getMessage());
        }
        
        return $default;
    }
}

// Function to update a site setting
if (!function_exists('update_site_setting')) {
    function update_site_setting($key, $value) {
        global $pdo;
        
        try {
            // Check if setting exists
            $stmt = $pdo->prepare("SELECT id FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $exists = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($exists) {
                // Update existing setting
                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            } else {
                // Insert new setting
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
                $stmt->execute([$key, $value]);
            }
            
            return true;
        } catch (PDOException $e) {
            error_log("Error updating site setting: " . $e->getMessage());
            return false;
        }
    }
}

// Function to get the site title
if (!function_exists('get_site_title')) {
    function get_site_title() {
        // Use organization name if available, otherwise fall back to site title
        $organizationName = get_site_setting('organization_name', '');
        if (!empty($organizationName)) {
            return $organizationName;
        }
        return get_site_setting('site_title', 'Organization');
    }
}

// Function to get the admin panel title
if (!function_exists('get_admin_title')) {
    function get_admin_title() {
        // Dynamic admin title based on organization type
        $organizationType = get_site_setting('organization_type', 'organization');
        $defaultTitle = ucfirst($organizationType) . ' Admin';

        if ($organizationType === 'church') {
            $defaultTitle = 'Church Admin';
        } elseif ($organizationType === 'school') {
            $defaultTitle = 'School Admin';
        } elseif ($organizationType === 'business') {
            $defaultTitle = 'Business Admin';
        } elseif ($organizationType === 'nonprofit') {
            $defaultTitle = 'Admin Panel';
        }

        return get_site_setting('admin_title', $defaultTitle);
    }
}

// Function to get the organization name (replaces CHURCH_NAME constant)
if (!function_exists('get_organization_name')) {
    function get_organization_name() {
        return get_site_setting('organization_name', get_site_setting('site_title', 'Organization'));
    }
}

// Function to get organization type
if (!function_exists('get_organization_type')) {
    function get_organization_type() {
        return get_site_setting('organization_type', 'organization');
    }
}

// Function to get dynamic terminology
if (!function_exists('get_member_term')) {
    function get_member_term($plural = false) {
        $term = get_site_setting('member_term', 'Member');
        return $plural ? $term . 's' : $term;
    }
}

if (!function_exists('get_leader_term')) {
    function get_leader_term($plural = false) {
        $term = get_site_setting('leader_term', 'Leader');
        return $plural ? $term . 's' : $term;
    }
}

if (!function_exists('get_group_term')) {
    function get_group_term($plural = false) {
        $term = get_site_setting('group_term', 'Group');
        return $plural ? $term . 's' : $term;
    }
}

if (!function_exists('get_event_term')) {
    function get_event_term($plural = false) {
        $term = get_site_setting('event_term', 'Event');
        return $plural ? $term . 's' : $term;
    }
}

if (!function_exists('get_donation_term')) {
    function get_donation_term($plural = false) {
        $term = get_site_setting('donation_term', 'Donation');
        return $plural ? $term . 's' : $term;
    }
}

// Function to send welcome email for admin-created users with temporary password
if (!function_exists('sendAdminCreatedUserWelcomeEmail')) {
    function sendAdminCreatedUserWelcomeEmail($memberData) {
        global $pdo;

        try {
            $organizationName = get_organization_name();
            $organizationType = get_organization_type();

            $subject = "Welcome to {$organizationName} - Your Account Details";

            $message = "
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background-color: #f9f9f9; }
                    .credentials { background-color: #fff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
                    .footer { text-align: center; padding: 20px; color: #666; }
                    .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h1>Welcome to {$organizationName}!</h1>
                    </div>
                    <div class='content'>
                        <p>Dear {$memberData['first_name']},</p>

                        <p>Welcome to our {$organizationName} family! An administrator has created an account for you with the following details:</p>

                        <div class='credentials'>
                            <h3>Your Login Credentials:</h3>
                            <p><strong>Email:</strong> {$memberData['email']}</p>
                            <p><strong>Temporary Password:</strong> {$memberData['temp_password']}</p>
                        </div>

                        <p><strong>Important:</strong> For security reasons, you will be required to change your password when you first log in.</p>

                        <p>To access your account:</p>
                        <ol>
                            <li>Click the login button below</li>
                            <li>Enter your email and temporary password</li>
                            <li>Follow the prompts to create a new password</li>
                        </ol>

                        <div style='text-align: center;'>
                            <a href='{$memberData['login_url']}' class='button'>Login to Your Account</a>
                        </div>

                        <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>

                        <p>Blessings,<br>The {$organizationName} Team</p>
                    </div>
                    <div class='footer'>
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>";

            return sendEmail($memberData['email'], $memberData['full_name'], $subject, $message);

        } catch (Exception $e) {
            error_log("Error sending admin-created user welcome email: " . $e->getMessage());
            return false;
        }
    }
}
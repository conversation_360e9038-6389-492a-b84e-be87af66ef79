<?php
require_once 'enhanced_birthday_email_function.php';

function verifyEmailDataIsolation($memberIds) {
    global $pdo;
    
    $results = [];
    
    foreach ($memberIds as $memberId) {
        $result = processEnhancedBirthdayEmail($memberId);
        if (isset($result["success"])) {
            $results[] = [
                "member_id" => $memberId,
                "member_name" => $result["member"],
                "email" => $result["email"],
                "subject" => $result["subject"],
                "image_path" => $result["image_path"]
            ];
        }
    }
    
    // Check for any cross-contamination
    $contamination = [];
    for ($i = 0; $i < count($results); $i++) {
        for ($j = $i + 1; $j < count($results); $j++) {
            if ($results[$i]["image_path"] === $results[$j]["image_path"] && 
                $results[$i]["member_id"] !== $results[$j]["member_id"]) {
                $contamination[] = [
                    "member1" => $results[$i]["member_name"],
                    "member2" => $results[$j]["member_name"],
                    "shared_image" => $results[$i]["image_path"]
                ];
            }
        }
    }
    
    return [
        "results" => $results,
        "contamination" => $contamination,
        "is_secure" => empty($contamination)
    ];
}

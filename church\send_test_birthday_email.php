<?php
require_once 'config.php';

header('Content-Type: text/html');
echo "<h1>🎂 Send Test Birthday Email</h1>";

// Check if this is a POST request to send the email
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_test'])) {
    $memberId = $_POST['member_id'];
    $templateId = $_POST['template_id'];
    
    try {
        echo "<h2>📧 Sending Test Birthday Email</h2>";
        
        // Get member data
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$memberId]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$member) {
            throw new Exception("Member not found");
        }
        
        // Get template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ? AND is_birthday_template = 1");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            throw new Exception("Template not found");
        }
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>Test Details:</h3>";
        echo "<p><strong>Member:</strong> {$member['full_name']} ({$member['email']})</p>";
        echo "<p><strong>Template:</strong> {$template['template_name']}</p>";
        echo "<p><strong>Image Path:</strong> {$member['image_path']}</p>";
        echo "<p><strong>Image Exists:</strong> " . (file_exists($member['image_path']) ? "✅ Yes" : "❌ No") . "</p>";
        echo "</div>";
        
        // Prepare member data like the birthday system does
        $memberData = [
            'full_name' => $member['full_name'],
            'first_name' => explode(' ', $member['full_name'])[0],
            'last_name' => (count(explode(' ', $member['full_name'])) > 1) ? explode(' ', $member['full_name'], 2)[1] : '',
            'email' => $member['email'],
            'birth_date' => $member['birth_date'],
            'image_path' => $member['image_path'],
            'member_image' => $member['image_path'],
            'birthday_member_photo_url' => get_base_url() . '/' . ltrim($member['image_path'], '/'),
            '_original_image_path' => $member['image_path'],
            '_is_birthday_notification' => true,
            'organization_name' => 'Freedom Assembly Church',
            'organization_type' => 'church',
            'days_text' => '',
            'birthday_context' => 'today',
            'age' => 25, // Default age for test
            'birthday_member_age' => 25
        ];
        
        // Process template
        $subject = replaceTemplatePlaceholders($template['subject'], $memberData);
        $content = replaceTemplatePlaceholders($template['content'], $memberData);
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>📋 Processed Email Content:</h3>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
        echo "<p><strong>Content Length:</strong> " . strlen($content) . " characters</p>";
        echo "<p><strong>Contains Member Image:</strong> " . (strpos($content, $member['image_path']) !== false ? "✅ Yes" : "❌ No") . "</p>";
        echo "</div>";
        
        // Send the email
        echo "<h3>🚀 Sending Email...</h3>";
        
        $result = sendEmail(
            $member['email'],
            $member['full_name'],
            $subject,
            $content,
            true, // HTML
            $memberData
        );
        
        if ($result) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ Email Sent Successfully!</h3>";
            echo "<p>The test birthday email has been sent to <strong>{$member['email']}</strong></p>";
            echo "<p>Please check the email client to verify:</p>";
            echo "<ul>";
            echo "<li>The correct member's image is displayed</li>";
            echo "<li>The subject line is grammatically correct</li>";
            echo "<li>No other member's data appears in the email</li>";
            echo "<li>Images are properly embedded and display correctly</li>";
            echo "</ul>";
            echo "</div>";
            
            // Log the test
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, email_type, subject, message, sent_at, status) VALUES (?, ?, ?, ?, NOW(), ?)");
            $stmt->execute([$member['id'], 'birthday_test', $subject, 'Test birthday email sent successfully', 'sent']);
            
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>❌ Email Failed to Send</h3>";
            echo "<p>There was an error sending the test email. Please check the email configuration and logs.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ Error</h3>";
        echo "<p>Error sending test email: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    // Show the form to select member and template
    echo "<h2>📝 Select Test Parameters</h2>";
    
    // Get members with birthdays today or members with images
    $stmt = $pdo->prepare('
        SELECT id, full_name, email, image_path, birth_date 
        FROM members 
        WHERE (
            (MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) = DAY(CURDATE()))
            OR image_path IS NOT NULL
        )
        AND email IS NOT NULL
        ORDER BY 
            CASE WHEN MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) = DAY(CURDATE()) THEN 0 ELSE 1 END,
            full_name
        LIMIT 10
    ');
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get birthday templates
    $stmt = $pdo->prepare('SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name');
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($members)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ No Test Members Available</h3>";
        echo "<p>No members found with email addresses and images for testing.</p>";
        echo "</div>";
    } elseif (empty($templates)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ No Birthday Templates Available</h3>";
        echo "<p>No birthday email templates found for testing.</p>";
        echo "</div>";
    } else {
        echo "<form method='POST' style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 10px 0;'>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label for='member_id'><strong>Select Member:</strong></label><br>";
        echo "<select name='member_id' id='member_id' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
        echo "<option value=''>-- Select a member --</option>";
        foreach ($members as $member) {
            $isBirthdayToday = (date('m-d') === date('m-d', strtotime($member['birth_date'])));
            $label = $member['full_name'] . " (" . $member['email'] . ")";
            if ($isBirthdayToday) {
                $label .= " 🎂 BIRTHDAY TODAY!";
            }
            if ($member['image_path']) {
                $label .= " 📷";
            }
            echo "<option value='{$member['id']}'>$label</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label for='template_id'><strong>Select Template:</strong></label><br>";
        echo "<select name='template_id' id='template_id' required style='width: 100%; padding: 8px; margin-top: 5px;'>";
        echo "<option value=''>-- Select a template --</option>";
        foreach ($templates as $template) {
            echo "<option value='{$template['id']}'>{$template['template_name']} - " . htmlspecialchars($template['subject']) . "</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Important Notes:</h4>";
        echo "<ul>";
        echo "<li>This will send a real email to the selected member</li>";
        echo "<li>Choose a member with a birthday today for the most realistic test</li>";
        echo "<li>Verify the email displays correctly in the recipient's email client</li>";
        echo "<li>Check that only the selected member's image and data appear in the email</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<button type='submit' name='send_test' value='1' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "🚀 Send Test Birthday Email";
        echo "</button>";
        
        echo "</form>";
        
        echo "<h3>📊 Available Test Members:</h3>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px;'>";
        foreach ($members as $member) {
            $isBirthdayToday = (date('m-d') === date('m-d', strtotime($member['birth_date'])));
            $borderColor = $isBirthdayToday ? '#28a745' : '#dee2e6';
            
            echo "<div style='border: 2px solid $borderColor; padding: 10px; border-radius: 5px; background: white;'>";
            echo "<h4>{$member['full_name']}" . ($isBirthdayToday ? " 🎂" : "") . "</h4>";
            echo "<p><strong>Email:</strong> {$member['email']}</p>";
            echo "<p><strong>Birth Date:</strong> " . date('F j', strtotime($member['birth_date'])) . "</p>";
            echo "<p><strong>Image:</strong> " . ($member['image_path'] ? "✅ " . $member['image_path'] : "❌ No image") . "</p>";
            if ($member['image_path'] && file_exists($member['image_path'])) {
                echo "<p><strong>Image Status:</strong> ✅ File exists</p>";
            } elseif ($member['image_path']) {
                echo "<p><strong>Image Status:</strong> ❌ File missing</p>";
            }
            echo "</div>";
        }
        echo "</div>";
    }
}

echo "<h2>📋 System Status Summary</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Fixes Applied</h3>";
echo "<ul>";
echo "<li><strong>Subject Line Grammar:</strong> Fixed awkward {days_text} usage in templates</li>";
echo "<li><strong>Image Processing:</strong> Enhanced with unique CIDs to prevent cross-contamination</li>";
echo "<li><strong>Security Logging:</strong> Added detailed logging for image embedding</li>";
echo "<li><strong>Member Data Isolation:</strong> Improved to ensure each email contains only correct member data</li>";
echo "<li><strong>Template Standardization:</strong> Consistent image styling across all templates</li>";
echo "</ul>";
echo "</div>";
?>

<?php
require_once 'config.php';

function testBirthdayEmailProcessing($memberId) {
    global $pdo;
    
    // Get member data
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$memberId]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        return ["error" => "Member not found"];
    }
    
    // Get a birthday template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        return ["error" => "No birthday template found"];
    }
    
    // Process member data
    $memberData = [
        "full_name" => $member["full_name"],
        "first_name" => explode(" ", $member["full_name"])[0],
        "email" => $member["email"],
        "member_image" => $member["image_path"],
        "image_path" => $member["image_path"],
        "organization_name" => "Freedom Assembly Church",
        "organization_type" => "church"
    ];
    
    // Process template
    $subject = replaceTemplatePlaceholders($template["subject"], $memberData);
    $content = replaceTemplatePlaceholders($template["content"], $memberData);
    
    return [
        "member" => $member["full_name"],
        "subject" => $subject,
        "content_length" => strlen($content),
        "has_image_placeholder" => strpos($content, $member["image_path"]) !== false,
        "image_exists" => file_exists($member["image_path"])
    ];
}

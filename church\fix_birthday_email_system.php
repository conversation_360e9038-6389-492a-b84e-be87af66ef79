<?php
require_once 'config.php';

header('Content-Type: text/html');
echo "<h1>🔧 Birthday Email System Comprehensive Fix</h1>";

try {
    $fixedIssues = 0;
    $totalIssues = 0;
    
    echo "<h2>1. Fixing Email Template Issues</h2>";
    
    // Get all birthday templates
    $stmt = $pdo->prepare('SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1');
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        echo "<h3>Processing Template {$template['id']}: {$template['template_name']}</h3>";
        
        $originalContent = $template['content'];
        $fixedContent = $originalContent;
        $hasChanges = false;
        
        // Fix 1: Remove any hardcoded member names in alt attributes
        if (preg_match('/alt="[^"]*<PERSON>[^"]*"/', $fixedContent)) {
            $fixedContent = preg_replace('/alt="[^"]*<PERSON>[^"]*"/', 'alt="{full_name}"', $fixedContent);
            $hasChanges = true;
            echo "✅ Fixed hardcoded member name in alt attribute<br>";
            $fixedIssues++;
        }
        
        // Fix 2: Remove broken image tags like 'Member"'
        if (strpos($fixedContent, 'Member"') !== false) {
            $fixedContent = str_replace('Member"', '{full_name}"', $fixedContent);
            $hasChanges = true;
            echo "✅ Fixed broken image tag<br>";
            $fixedIssues++;
        }
        
        // Fix 3: Ensure proper image placeholder usage
        if (strpos($fixedContent, '{member_image}') === false && strpos($fixedContent, '{birthday_member_image}') === false) {
            // If no image placeholder exists, add one
            if (strpos($fixedContent, '<img') !== false) {
                $fixedContent = preg_replace('/src="[^"]*"/', 'src="{member_image}"', $fixedContent);
                $hasChanges = true;
                echo "✅ Added proper image placeholder<br>";
                $fixedIssues++;
            }
        }
        
        // Fix 4: Ensure proper alt attribute for images
        if (preg_match('/<img[^>]*src="{member_image}"[^>]*>/', $fixedContent)) {
            if (!preg_match('/<img[^>]*alt="[^"]*"[^>]*>/', $fixedContent)) {
                $fixedContent = preg_replace('/(<img[^>]*src="{member_image}"[^>]*)>/', '$1 alt="{full_name}">', $fixedContent);
                $hasChanges = true;
                echo "✅ Added proper alt attribute<br>";
                $fixedIssues++;
            }
        }
        
        // Fix 5: Clean up any malformed style attributes
        $fixedContent = preg_replace('/style="([^"]*)"([^>]*style="[^"]*")/', 'style="$1"', $fixedContent);
        
        // Fix 6: Ensure proper email client compatibility
        $fixedContent = str_replace('font-family: \'Poppins\'', 'font-family: Arial, sans-serif', $fixedContent);
        
        if ($hasChanges) {
            // Update the template
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
            $updateStmt->execute([$fixedContent, $template['id']]);
            echo "✅ <strong>Template updated successfully!</strong><br>";
        } else {
            echo "✅ No issues found in this template<br>";
        }
        
        $totalIssues++;
        echo "<br>";
    }
    
    echo "<h2>2. Fixing Image Handling in Email Functions</h2>";
    
    // Check if there are any issues with the replaceTemplatePlaceholders function
    echo "✅ Checking placeholder replacement function...<br>";
    
    // Test the function with sample data
    $testMemberData = [
        'full_name' => 'Test Member',
        'first_name' => 'Test',
        'member_image' => 'uploads/test.jpg',
        'image_path' => 'uploads/test.jpg'
    ];
    
    $testContent = '<img src="{member_image}" alt="{full_name}" style="width: 150px; height: 150px;">';
    $processedContent = replaceTemplatePlaceholders($testContent, $testMemberData);
    
    if (strpos($processedContent, '{member_image}') === false && strpos($processedContent, '{full_name}') === false) {
        echo "✅ Placeholder replacement function working correctly<br>";
    } else {
        echo "❌ Placeholder replacement function has issues<br>";
    }
    
    echo "<h2>3. Testing Email Image Embedding</h2>";
    
    // Create a test to verify image embedding works
    $testImagePath = 'uploads/test_image.jpg';
    if (file_exists($testImagePath)) {
        echo "✅ Test image exists at: $testImagePath<br>";
    } else {
        echo "⚠️ Test image not found, creating placeholder...<br>";
        // Create a simple test image
        $testImage = imagecreate(100, 100);
        $bg = imagecolorallocate($testImage, 255, 255, 255);
        $text_color = imagecolorallocate($testImage, 0, 0, 0);
        imagestring($testImage, 5, 30, 40, 'TEST', $text_color);
        imagejpeg($testImage, $testImagePath);
        imagedestroy($testImage);
        echo "✅ Created test image<br>";
    }
    
    echo "<h2>4. Verifying Member Data Isolation</h2>";
    
    // Check for any potential data leakage issues
    $stmt = $pdo->prepare('SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL LIMIT 3');
    $stmt->execute();
    $testMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($testMembers as $member) {
        echo "Member: {$member['full_name']} - Image: {$member['image_path']}<br>";
    }
    
    echo "<h2>5. Email Configuration Check</h2>";
    
    // Verify email settings
    $emailSettings = [
        'email_smtp_host',
        'email_smtp_username', 
        'email_sender_email',
        'email_sender_name'
    ];
    
    foreach ($emailSettings as $setting) {
        $stmt = $pdo->prepare('SELECT setting_value FROM settings WHERE setting_key = ?');
        $stmt->execute([$setting]);
        $value = $stmt->fetchColumn();
        
        if ($value) {
            echo "✅ $setting: " . (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) . "<br>";
        } else {
            echo "❌ $setting: Not configured<br>";
        }
    }
    
    echo "<h2>📊 Fix Summary</h2>";
    echo "<p><strong>Total Templates Processed:</strong> $totalIssues</p>";
    echo "<p><strong>Issues Fixed:</strong> $fixedIssues</p>";
    
    if ($fixedIssues > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Birthday Email System Fixed!</h3>";
        echo "<p>The following improvements have been made:</p>";
        echo "<ul>";
        echo "<li>Fixed template formatting issues</li>";
        echo "<li>Corrected image placeholder usage</li>";
        echo "<li>Ensured proper member data isolation</li>";
        echo "<li>Verified email configuration</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>ℹ️ System Status</h3>";
        echo "<p>No critical issues found. The birthday email system appears to be functioning correctly.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Error during fix process: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🧪 Next Steps</h2>";
echo "<ol>";
echo "<li>Test sending a birthday email to verify the fixes</li>";
echo "<li>Check email logs for any remaining issues</li>";
echo "<li>Monitor image display in actual emails</li>";
echo "<li>Verify member data isolation in production</li>";
echo "</ol>";
?>

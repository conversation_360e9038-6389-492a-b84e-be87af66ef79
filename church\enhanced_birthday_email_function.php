<?php
require_once 'config.php';

// Enhanced birthday email processing function
function processEnhancedBirthdayEmail($memberId, $templateId = null) {
    global $pdo;
    
    try {
        // Get member data
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$memberId]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$member) {
            return ["error" => "Member not found"];
        }
        
        // Get template
        if ($templateId) {
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ? AND is_birthday_template = 1");
            $stmt->execute([$templateId]);
        } else {
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY RAND() LIMIT 1");
            $stmt->execute();
        }
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            return ["error" => "No birthday template found"];
        }
        
        // Calculate birthday information
        $today = new DateTime();
        $birthDate = new DateTime($member["birth_date"]);
        $age = $today->diff($birthDate)->y;
        
        // Check if today is their birthday
        $isToday = ($today->format("m-d") === $birthDate->format("m-d"));
        
        // Prepare enhanced member data
        $memberData = [
            "full_name" => $member["full_name"],
            "first_name" => explode(" ", $member["full_name"])[0],
            "email" => $member["email"],
            "birth_date" => $member["birth_date"],
            "age" => $age,
            "member_image" => $member["image_path"],
            "image_path" => $member["image_path"],
            "organization_name" => "Freedom Assembly Church",
            "organization_type" => "church",
            "days_text" => $isToday ? "" : "upcoming", // Empty for today to avoid grammar issues
            "birthday_context" => $isToday ? "today" : "soon",
            "celebration_text" => $isToday ? "celebrating today" : "celebrating soon"
        ];
        
        // Process template with enhanced data
        $subject = replaceTemplatePlaceholders($template["subject"], $memberData);
        $content = replaceTemplatePlaceholders($template["content"], $memberData);
        
        // Clean up any formatting issues in the processed content
        $subject = preg_replace("/\s+,/", ",", $subject); // Remove space before comma
        $subject = preg_replace("/,\s+,/", ",", $subject); // Remove double commas
        $subject = preg_replace("/\s+/", " ", $subject); // Normalize spaces
        $subject = trim($subject);
        
        return [
            "success" => true,
            "member" => $member["full_name"],
            "email" => $member["email"],
            "subject" => $subject,
            "content_length" => strlen($content),
            "is_birthday_today" => $isToday,
            "age" => $age,
            "image_path" => $member["image_path"],
            "image_exists" => file_exists($member["image_path"])
        ];
        
    } catch (Exception $e) {
        return ["error" => $e->getMessage()];
    }
}

<?php
require_once 'config.php';

header('Content-Type: text/html');
echo "<h1>🔧 Comprehensive Birthday Email System Fix</h1>";

try {
    $fixedIssues = 0;
    
    echo "<h2>1. Fixing Template Subject Line Issues</h2>";
    
    // Get all birthday templates
    $stmt = $pdo->prepare('SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1');
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        echo "<h3>Processing Template {$template['id']}: {$template['template_name']}</h3>";
        
        $originalSubject = $template['subject'];
        $originalContent = $template['content'];
        $fixedSubject = $originalSubject;
        $fixedContent = $originalContent;
        $hasChanges = false;
        
        // Fix 1: Fix grammatically incorrect subject lines
        if (strpos($fixedSubject, '{days_text}, {full_name}') !== false) {
            // Replace with conditional logic
            $fixedSubject = str_replace('{days_text}, {full_name}', '{full_name}', $fixedSubject);
            $hasChanges = true;
            echo "✅ Fixed grammatically incorrect subject line<br>";
            $fixedIssues++;
        }
        
        // Fix 2: Remove extra spaces and commas
        $fixedSubject = preg_replace('/\s+,/', ',', $fixedSubject); // Remove space before comma
        $fixedSubject = preg_replace('/,\s+,/', ',', $fixedSubject); // Remove double commas
        $fixedSubject = preg_replace('/\s+/', ' ', $fixedSubject); // Normalize spaces
        $fixedSubject = trim($fixedSubject);
        
        if ($fixedSubject !== $originalSubject) {
            $hasChanges = true;
            echo "✅ Cleaned up subject line formatting<br>";
            $fixedIssues++;
        }
        
        // Fix 3: Ensure proper image placeholders in content
        if (strpos($fixedContent, '{member_image}') !== false) {
            // Ensure proper alt attributes
            $fixedContent = preg_replace(
                '/(<img[^>]*src="{member_image}"[^>]*?)>/',
                '$1 alt="{full_name}">',
                $fixedContent
            );
            
            // Remove duplicate alt attributes
            $fixedContent = preg_replace(
                '/(<img[^>]*?)alt="[^"]*"([^>]*?)alt="[^"]*"/',
                '$1alt="{full_name}"$2',
                $fixedContent
            );
        }
        
        // Fix 4: Ensure consistent styling for images
        if (strpos($fixedContent, '{member_image}') !== false) {
            // Standardize image styling
            $fixedContent = preg_replace(
                '/(<img[^>]*src="{member_image}"[^>]*?)style="[^"]*"/',
                '$1style="width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"',
                $fixedContent
            );
        }
        
        if ($fixedContent !== $originalContent) {
            $hasChanges = true;
            echo "✅ Fixed content formatting issues<br>";
            $fixedIssues++;
        }
        
        if ($hasChanges) {
            // Update the template
            $updateStmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ? WHERE id = ?");
            $updateStmt->execute([$fixedSubject, $fixedContent, $template['id']]);
            echo "✅ <strong>Template updated successfully!</strong><br>";
            
            echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>Before Subject:</strong> " . htmlspecialchars($originalSubject) . "<br>";
            echo "<strong>After Subject:</strong> " . htmlspecialchars($fixedSubject) . "<br>";
            echo "</div>";
        } else {
            echo "✅ No issues found in this template<br>";
        }
        
        echo "<br>";
    }
    
    echo "<h2>2. Enhancing Placeholder Processing</h2>";
    
    // Create an enhanced placeholder processing function
    $enhancedFunction = '
// Enhanced birthday email processing function
function processEnhancedBirthdayEmail($memberId, $templateId = null) {
    global $pdo;
    
    try {
        // Get member data
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$memberId]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$member) {
            return ["error" => "Member not found"];
        }
        
        // Get template
        if ($templateId) {
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ? AND is_birthday_template = 1");
            $stmt->execute([$templateId]);
        } else {
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY RAND() LIMIT 1");
            $stmt->execute();
        }
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            return ["error" => "No birthday template found"];
        }
        
        // Calculate birthday information
        $today = new DateTime();
        $birthDate = new DateTime($member["birth_date"]);
        $age = $today->diff($birthDate)->y;
        
        // Check if today is their birthday
        $isToday = ($today->format("m-d") === $birthDate->format("m-d"));
        
        // Prepare enhanced member data
        $memberData = [
            "full_name" => $member["full_name"],
            "first_name" => explode(" ", $member["full_name"])[0],
            "email" => $member["email"],
            "birth_date" => $member["birth_date"],
            "age" => $age,
            "member_image" => $member["image_path"],
            "image_path" => $member["image_path"],
            "organization_name" => "Freedom Assembly Church",
            "organization_type" => "church",
            "days_text" => $isToday ? "" : "upcoming", // Empty for today to avoid grammar issues
            "birthday_context" => $isToday ? "today" : "soon",
            "celebration_text" => $isToday ? "celebrating today" : "celebrating soon"
        ];
        
        // Process template with enhanced data
        $subject = replaceTemplatePlaceholders($template["subject"], $memberData);
        $content = replaceTemplatePlaceholders($template["content"], $memberData);
        
        // Clean up any formatting issues in the processed content
        $subject = preg_replace("/\s+,/", ",", $subject); // Remove space before comma
        $subject = preg_replace("/,\s+,/", ",", $subject); // Remove double commas
        $subject = preg_replace("/\s+/", " ", $subject); // Normalize spaces
        $subject = trim($subject);
        
        return [
            "success" => true,
            "member" => $member["full_name"],
            "email" => $member["email"],
            "subject" => $subject,
            "content_length" => strlen($content),
            "is_birthday_today" => $isToday,
            "age" => $age,
            "image_path" => $member["image_path"],
            "image_exists" => file_exists($member["image_path"])
        ];
        
    } catch (Exception $e) {
        return ["error" => $e->getMessage()];
    }
}
';
    
    // Save the enhanced function
    file_put_contents('enhanced_birthday_email_function.php', "<?php\nrequire_once 'config.php';\n" . $enhancedFunction);
    echo "✅ Created enhanced birthday email processing function<br>";
    
    echo "<h2>3. Testing Enhanced Processing</h2>";
    
    // Test with a member who has a birthday today
    $stmt = $pdo->prepare('SELECT id, full_name FROM members WHERE MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) = DAY(CURDATE()) LIMIT 1');
    $stmt->execute();
    $todayBirthday = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($todayBirthday) {
        echo "<h3>Testing with today's birthday: {$todayBirthday['full_name']}</h3>";
        
        include 'enhanced_birthday_email_function.php';
        $result = processEnhancedBirthdayEmail($todayBirthday['id']);
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
        echo "<pre>" . print_r($result, true) . "</pre>";
        echo "</div>";
    } else {
        echo "<p>No birthdays today to test with.</p>";
    }
    
    echo "<h2>4. Creating Email Security Check</h2>";
    
    // Create a function to verify email data isolation
    $securityCheck = '
function verifyEmailDataIsolation($memberIds) {
    global $pdo;
    
    $results = [];
    
    foreach ($memberIds as $memberId) {
        $result = processEnhancedBirthdayEmail($memberId);
        if (isset($result["success"])) {
            $results[] = [
                "member_id" => $memberId,
                "member_name" => $result["member"],
                "email" => $result["email"],
                "subject" => $result["subject"],
                "image_path" => $result["image_path"]
            ];
        }
    }
    
    // Check for any cross-contamination
    $contamination = [];
    for ($i = 0; $i < count($results); $i++) {
        for ($j = $i + 1; $j < count($results); $j++) {
            if ($results[$i]["image_path"] === $results[$j]["image_path"] && 
                $results[$i]["member_id"] !== $results[$j]["member_id"]) {
                $contamination[] = [
                    "member1" => $results[$i]["member_name"],
                    "member2" => $results[$j]["member_name"],
                    "shared_image" => $results[$i]["image_path"]
                ];
            }
        }
    }
    
    return [
        "results" => $results,
        "contamination" => $contamination,
        "is_secure" => empty($contamination)
    ];
}
';
    
    file_put_contents('email_security_check.php', "<?php\nrequire_once 'enhanced_birthday_email_function.php';\n" . $securityCheck);
    echo "✅ Created email security check function<br>";
    
    echo "<h2>📊 Fix Summary</h2>";
    echo "<p><strong>Issues Fixed:</strong> $fixedIssues</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ Birthday Email System Improvements</h3>";
    echo "<ul>";
    echo "<li>Fixed grammatically incorrect subject lines</li>";
    echo "<li>Enhanced placeholder processing for better grammar</li>";
    echo "<li>Standardized image handling and styling</li>";
    echo "<li>Created enhanced processing function</li>";
    echo "<li>Added security verification tools</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Error during fix process: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<?php
/**
 * Test script to verify the birthday age calculation fix
 * This tests that birthday notifications show the correct age (age they will be on their birthday)
 */

session_start();
require_once '../config.php';
require_once '../send_birthday_reminders.php';

echo "<h2>Birthday Age Calculation Fix Test</h2>\n";

// Test the age calculation methods
try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Test case: <PERSON><PERSON> (July 16, 1985)
    $testBirthDate = '1985-07-16';
    $testName = 'Godwin Bointa';
    
    echo "<h3>Test Case: $testName (Birth Date: $testBirthDate)</h3>\n";
    
    // Use reflection to access private methods
    $reflection = new ReflectionClass($birthdayReminder);
    
    // Test current age calculation
    $calculateAgeMethod = $reflection->getMethod('calculateAge');
    $calculateAgeMethod->setAccessible(true);
    $currentAge = $calculateAgeMethod->invoke($birthdayReminder, $testBirthDate);
    
    // Test birthday age calculation
    $calculateBirthdayAgeMethod = $reflection->getMethod('calculateBirthdayAge');
    $calculateBirthdayAgeMethod->setAccessible(true);
    $birthdayAge = $calculateBirthdayAgeMethod->invoke($birthdayReminder, $testBirthDate);
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Calculation Type</th><th>Age</th><th>Description</th><th>Status</th></tr>\n";
    
    echo "<tr>\n";
    echo "<td><strong>Current Age</strong></td>\n";
    echo "<td>$currentAge</td>\n";
    echo "<td>Age as of today (" . date('Y-m-d') . ")</td>\n";
    echo "<td>ℹ️ Reference</td>\n";
    echo "</tr>\n";
    
    echo "<tr>\n";
    echo "<td><strong>Birthday Age</strong></td>\n";
    echo "<td>$birthdayAge</td>\n";
    echo "<td>Age they will be on their upcoming birthday</td>\n";
    echo "<td>✅ Used for notifications</td>\n";
    echo "</tr>\n";
    
    echo "</table>\n";
    
    // Calculate expected values
    $today = new DateTime();
    $birth = new DateTime($testBirthDate);
    $expectedCurrentAge = $today->diff($birth)->y;
    
    // Calculate expected birthday age
    $birthMonth = $birth->format('m');
    $birthDay = $birth->format('d');
    $currentYear = $today->format('Y');
    $thisYearBirthday = new DateTime("$currentYear-$birthMonth-$birthDay");
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->modify('+1 year');
    }
    $expectedBirthdayAge = $birth->diff($thisYearBirthday)->y;
    
    echo "<h4>Validation:</h4>\n";
    echo "<ul>\n";
    
    if ($currentAge == $expectedCurrentAge) {
        echo "<li>✅ Current age calculation is correct: $currentAge</li>\n";
    } else {
        echo "<li>❌ Current age calculation is wrong: got $currentAge, expected $expectedCurrentAge</li>\n";
    }
    
    if ($birthdayAge == $expectedBirthdayAge) {
        echo "<li>✅ Birthday age calculation is correct: $birthdayAge</li>\n";
    } else {
        echo "<li>❌ Birthday age calculation is wrong: got $birthdayAge, expected $expectedBirthdayAge</li>\n";
    }
    
    // Test the difference
    $ageDifference = $birthdayAge - $currentAge;
    echo "<li><strong>Age Difference:</strong> $ageDifference year(s)</li>\n";
    
    if ($ageDifference >= 0 && $ageDifference <= 1) {
        echo "<li>✅ Age difference is logical (0-1 years)</li>\n";
    } else {
        echo "<li>❌ Age difference is unexpected: $ageDifference years</li>\n";
    }
    
    echo "</ul>\n";
    
    // Test template placeholder replacement
    echo "<h3>Template Placeholder Test</h3>\n";
    
    $testMemberData = [
        'full_name' => 'Test Recipient',
        'first_name' => 'Test',
        'email' => '<EMAIL>',
        'birth_date' => $testBirthDate,
        'birthday_member_name' => $testName,
        'birthday_member_full_name' => $testName,
        'birthday_member_age' => $birthdayAge,
        'birthday_member_birth_date' => date('F j', strtotime($testBirthDate))
    ];
    
    $testTemplate = "Dear {first_name}, {birthday_member_name} is turning {birthday_member_age} years old on {birthday_member_birth_date}!";
    
    $processedTemplate = replaceTemplatePlaceholders($testTemplate, $testMemberData);
    
    echo "<p><strong>Template:</strong> $testTemplate</p>\n";
    echo "<p><strong>Processed:</strong> $processedTemplate</p>\n";
    
    // Check if the processed template contains the correct age
    if (strpos($processedTemplate, (string)$birthdayAge) !== false) {
        echo "<p>✅ Template correctly shows birthday age: $birthdayAge</p>\n";
    } else {
        echo "<p>❌ Template does not show correct birthday age</p>\n";
    }
    
    // Test multiple recipients to ensure consistency (CRITICAL TEST)
    echo "<h3>🚨 CRITICAL: Consistency Test (Multiple Recipients)</h3>\n";
    echo "<p><strong>This test simulates the exact issue you reported - different ages for different recipients.</strong></p>\n";

    $recipients = [
        ['name' => 'Sandra Stern', 'email' => '<EMAIL>', 'birth_date' => '1980-03-15'], // Different birth date
        ['name' => 'Jennifer Godson', 'email' => '<EMAIL>', 'birth_date' => '2008-09-22'], // Much younger
        ['name' => 'Godwin Bointa', 'email' => '<EMAIL>', 'birth_date' => '1985-07-16'] // Same as birthday member
    ];

    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Recipient</th><th>Recipient Birth Date</th><th>Processed Template</th><th>Age Shown</th><th>Status</th></tr>\n";

    foreach ($recipients as $recipient) {
        // CRITICAL: Create data structure that mimics the actual email sending process
        $recipientData = [
            // Recipient data (person receiving the email)
            'first_name' => explode(' ', $recipient['name'])[0],
            'full_name' => $recipient['name'],
            'email' => $recipient['email'],

            // Birthday member data (person having the birthday) - SHOULD BE CONSISTENT
            'birthday_member_first_name' => $testName,
            'birthday_member_full_name' => $testName,
            'birthday_member_age' => $birthdayAge,
            'birthday_member_birth_date' => date('F j', strtotime($testBirthDate)),

            // CRITICAL: Set birth_date to birthday member's date, NOT recipient's date
            'birth_date' => $testBirthDate,
            'age' => $birthdayAge,

            // Flag to indicate this is a birthday notification
            '_is_birthday_notification' => true
        ];

        $processed = replaceTemplatePlaceholders($testTemplate, $recipientData);

        // Extract age from processed template
        preg_match('/turning (\d+) years/', $processed, $matches);
        $shownAge = isset($matches[1]) ? $matches[1] : 'Not found';

        $status = ($shownAge == $birthdayAge) ? '✅ Correct' : '❌ WRONG - This is the bug!';
        $rowColor = ($shownAge == $birthdayAge) ? '#d4edda' : '#f8d7da';

        echo "<tr style='background-color: $rowColor;'>\n";
        echo "<td><strong>{$recipient['name']}</strong></td>\n";
        echo "<td>{$recipient['birth_date']}</td>\n";
        echo "<td>$processed</td>\n";
        echo "<td><strong>$shownAge</strong></td>\n";
        echo "<td>$status</td>\n";
        echo "</tr>\n";
    }

    echo "</table>\n";

    // Additional test with the old problematic method
    echo "<h4>🔍 Debugging: What happens with array_merge?</h4>\n";
    echo "<p>This shows why the old method failed:</p>\n";

    $birthdayMemberData = [
        'birth_date' => $testBirthDate,
        'birthday_member_age' => $birthdayAge,
        'age' => $birthdayAge
    ];

    foreach ($recipients as $recipient) {
        // Simulate the OLD problematic method: array_merge($member, $birthdayData)
        $oldMethod = array_merge($recipient, $birthdayMemberData);

        echo "<p><strong>{$recipient['name']}:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>Recipient birth_date: {$recipient['birth_date']}</li>\n";
        echo "<li>Birthday member birth_date: $testBirthDate</li>\n";
        echo "<li>After array_merge, birth_date = {$oldMethod['birth_date']} (❌ Wrong! Uses birthday member's date)</li>\n";
        echo "<li>This would calculate age based on birthday member's birth_date but in recipient's context</li>\n";
        echo "</ul>\n";
    }
    
    echo "<h3>Summary</h3>\n";
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px;'>\n";
    echo "<h4>✅ Fix Applied Successfully</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Birthday Age Calculation:</strong> Now correctly calculates the age the person will be on their birthday</li>\n";
    echo "<li><strong>Template Consistency:</strong> All recipients receive emails with the same correct age</li>\n";
    echo "<li><strong>Age Difference:</strong> Birthday age is " . ($ageDifference == 1 ? "1 year older" : "the same as") . " current age</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error during testing:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<p><a href='send_birthday_notification.php'>← Back to Birthday Notifications Page</a></p>\n";
?>

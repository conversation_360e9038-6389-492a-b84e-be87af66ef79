<?php
/**
 * Test script to verify the success/error message handling fix
 * in admin/send_birthday_notification.php
 */

echo "<h2>Testing Birthday Notification Message Handling Logic</h2>\n";

// Test different return scenarios from sendMemberBirthdayNotifications()
$test_scenarios = [
    'successful_send' => [
        'success' => 5,
        'failed' => 0,
        'skipped' => 2,
        'birthday_member' => '<PERSON> Doe',
        'template' => 'Birthday Notification Template'
    ],
    'partial_success' => [
        'success' => 3,
        'failed' => 2,
        'skipped' => 1,
        'birthday_member' => '<PERSON>',
        'template' => 'Birthday Notification Template'
    ],
    'no_recipients' => [
        'success' => 0,
        'failed' => 0,
        'skipped' => 0,
        'message' => 'No eligible recipients found'
    ],
    'template_error' => [
        'error' => 'Template not found',
        'success' => 0,
        'failed' => 0,
        'skipped' => 1
    ],
    'member_not_found' => [
        'error' => 'Birthday member not found',
        'success' => 0,
        'failed' => 0,
        'skipped' => 1
    ],
    'zero_success_no_message' => [
        'success' => 0,
        'failed' => 0,
        'skipped' => 5
    ]
];

function test_message_logic($result) {
    $success_message = '';
    $error_message = '';
    
    // Apply the same logic as in the fixed admin/send_birthday_notification.php
    if (isset($result['error'])) {
        $error_message = $result['error'];
    } elseif (isset($result['success']) && $result['success'] > 0) {
        // Success: At least one email was sent successfully
        $success_message = "Successfully sent {$result['success']} notification emails about {$result['birthday_member']}'s birthday!";
        if (isset($result['failed']) && $result['failed'] > 0) {
            $success_message .= " ({$result['failed']} failed)";
        }
    } elseif (isset($result['message'])) {
        // No emails sent but with a specific message (e.g., "No eligible recipients found")
        $error_message = $result['message'];
    } else {
        // No emails sent and no specific message
        $error_message = "No notification emails were sent. Please check if there are eligible recipients.";
    }
    
    return [
        'success_message' => $success_message,
        'error_message' => $error_message,
        'message_type' => !empty($success_message) ? 'success' : 'error',
        'display_message' => !empty($success_message) ? $success_message : $error_message
    ];
}

echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background-color: #f0f0f0;'>\n";
echo "<th>Scenario</th><th>Input Result</th><th>Message Type</th><th>Display Message</th><th>Status</th>\n";
echo "</tr>\n";

foreach ($test_scenarios as $scenario_name => $result) {
    $test_result = test_message_logic($result);
    
    // Determine expected behavior
    $expected_type = '';
    switch ($scenario_name) {
        case 'successful_send':
        case 'partial_success':
            $expected_type = 'success';
            break;
        case 'no_recipients':
        case 'template_error':
        case 'member_not_found':
        case 'zero_success_no_message':
            $expected_type = 'error';
            break;
    }
    
    $status = ($test_result['message_type'] === $expected_type) ? '✅ PASS' : '❌ FAIL';
    $row_color = ($test_result['message_type'] === 'success') ? '#d4edda' : '#f8d7da';
    
    echo "<tr style='background-color: $row_color;'>\n";
    echo "<td><strong>$scenario_name</strong></td>\n";
    echo "<td><pre>" . htmlspecialchars(print_r($result, true)) . "</pre></td>\n";
    echo "<td><strong>{$test_result['message_type']}</strong></td>\n";
    echo "<td>{$test_result['display_message']}</td>\n";
    echo "<td>$status</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>Test Summary</h3>\n";
echo "<p>This test verifies that the message handling logic correctly:</p>\n";
echo "<ul>\n";
echo "<li>✅ Shows <strong>success messages</strong> when emails are actually sent (success > 0)</li>\n";
echo "<li>✅ Shows <strong>error messages</strong> when there are actual errors (error key present)</li>\n";
echo "<li>✅ Shows <strong>error messages</strong> when no emails are sent (success = 0)</li>\n";
echo "<li>✅ Includes failure counts in success messages when applicable</li>\n";
echo "<li>✅ Handles edge cases like no recipients or missing templates</li>\n";
echo "</ul>\n";

echo "<h3>Key Fix Applied</h3>\n";
echo "<p>The fix changes the logic from:</p>\n";
echo "<pre style='background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>\n";
echo "// OLD (BROKEN) LOGIC:\n";
echo "if (isset(\$result['error'])) {\n";
echo "    \$error_message = \$result['error'];\n";
echo "} else {\n";
echo "    // Always shows success even if 0 emails sent!\n";
echo "    \$success_message = \"Successfully sent...\";\n";
echo "}\n";
echo "</pre>\n";

echo "<p>To:</p>\n";
echo "<pre style='background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>\n";
echo "// NEW (FIXED) LOGIC:\n";
echo "if (isset(\$result['error'])) {\n";
echo "    \$error_message = \$result['error'];\n";
echo "} elseif (isset(\$result['success']) && \$result['success'] > 0) {\n";
echo "    // Only shows success if emails were actually sent!\n";
echo "    \$success_message = \"Successfully sent...\";\n";
echo "} else {\n";
echo "    // Shows error when no emails sent\n";
echo "    \$error_message = \"No notification emails were sent...\";\n";
echo "}\n";
echo "</pre>\n";

echo "<p><a href='send_birthday_notification.php'>← Back to Birthday Notifications Page</a></p>\n";
?>
